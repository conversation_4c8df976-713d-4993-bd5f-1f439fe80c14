import { useState } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Card } from "../ui/card";
import { Upload, MapPin, Clock, CreditCard, CheckCircle, Building2 } from "lucide-react";
import { User } from "../../App";

interface VendorOnboardingProps {
  user: User;
  onComplete: () => void;
  isRegistration?: boolean;
}

export function VendorOnboarding({ user, onComplete, isRegistration = false }: VendorOnboardingProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isPaymentProcessing, setIsPaymentProcessing] = useState(false);
  const [formData, setFormData] = useState({
    businessName: "",
    description: "",
    services: "",
    category: "",
    subCategory: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    operatingHours: {
      monday: { open: "09:00", close: "18:00" },
      tuesday: { open: "09:00", close: "18:00" },
      wednesday: { open: "09:00", close: "18:00" },
      thursday: { open: "09:00", close: "18:00" },
      friday: { open: "09:00", close: "18:00" },
      saturday: { open: "10:00", close: "16:00" },
      sunday: { open: "closed", close: "closed" },
    },
    images: [],
    documents: [],
  });

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    if (currentStep === 5) {
      handleSubmit();
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleSkip = () => {
    if (currentStep === 5) {
      handleSubmit();
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleCompleteNow = () => {
    onComplete();
  };

  const handlePayment = () => {
    setIsPaymentProcessing(true);
    setTimeout(() => {
      setIsPaymentProcessing(false);
      setCurrentStep(5);
    }, 2000);
  };

  const handleSubmit = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      onComplete();
    }, 1500);
  };

  const categories = [
    "Restaurants & Food",
    "Retail & Shopping", 
    "Services",
    "Healthcare",
    "Technology",
    "Construction",
  ];

  const subCategories = {
    "Restaurants & Food": ["Fast Food", "Fine Dining", "Cafes", "Catering"],
    "Retail & Shopping": ["Clothing", "Electronics", "Home & Garden", "Groceries"],
    "Services": ["Consulting", "Repair", "Cleaning", "Transportation"],
    "Healthcare": ["Clinics", "Dentistry", "Pharmacy", "Wellness"],
    "Technology": ["IT Services", "Software", "Hardware", "Web Development"],
    "Construction": ["Building", "Renovation", "Plumbing", "Electrical"],
  };

  const steps = [
    { title: "Business Info", description: "Tell us about your business" },
    { title: "Gallery", description: "Upload photos and documents" },
    { title: "Location", description: "Set your business location" },
    { title: "Payment", description: "Pay the joining fee" },
    { title: "Submit", description: "Submit for approval" },
  ];

  if (currentStep === 5) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 flex items-center justify-center p-4">
        <Card className="w-full max-w-md p-8 rounded-2xl border shadow-lg text-center">
          <div className="space-y-6">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-foreground mb-2">
                {isRegistration ? "Welcome to VendorHub!" : "Submission Complete!"}
              </h2>
              <p className="text-muted-foreground">
                {isRegistration 
                  ? "Your account has been created successfully. You can complete your business profile anytime from your dashboard settings."
                  : "Your business profile has been submitted for admin approval. You'll receive a notification once it's reviewed."
                }
              </p>
            </div>
            
            {isRegistration ? (
              <div className="space-y-3">
                <Button 
                  onClick={onComplete}
                  disabled={isLoading}
                  className="w-full h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
                >
                  {isLoading ? "Setting up..." : "Continue to Dashboard"}
                </Button>
                <p className="text-xs text-muted-foreground text-center">
                  Complete your profile later in Settings → Business Profile
                </p>
              </div>
            ) : (
              <Button 
                onClick={onComplete}
                disabled={isLoading}
                className="w-full h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
              >
                {isLoading ? "Setting up..." : "Continue to Dashboard"}
              </Button>
            )}
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 p-4">
      <div className="max-w-2xl mx-auto space-y-6 py-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-primary rounded-2xl flex items-center justify-center">
            <Building2 className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">
              {isRegistration ? "Welcome to VendorHub!" : "Business Onboarding"}
            </h1>
            <p className="text-muted-foreground">
              {isRegistration 
                ? "Let's set up your business profile (you can skip and complete later)" 
                : "Complete your vendor profile setup"
              }
            </p>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex justify-between items-center">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center flex-1">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                index + 1 <= currentStep 
                  ? "bg-primary text-white" 
                  : "bg-muted text-muted-foreground"
              }`}>
                {index + 1}
              </div>
              <div className="text-center mt-2">
                <div className="text-xs font-medium text-foreground">{step.title}</div>
                <div className="text-xs text-muted-foreground hidden sm:block">{step.description}</div>
              </div>
              {index < steps.length - 1 && (
                <div className={`h-1 w-full mt-4 ${
                  index + 1 < currentStep ? "bg-primary" : "bg-muted"
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Form Content */}
        <Card className="p-6 rounded-2xl border shadow-lg">
          <div className="space-y-6">
            {/* Step 1: Business Info */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold">Business Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="businessName">Business Name *</Label>
                    <Input
                      id="businessName"
                      placeholder="Your Business Name"
                      value={formData.businessName}
                      onChange={(e) => updateFormData("businessName", e.target.value)}
                      className="rounded-xl h-12"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select 
                      value={formData.category} 
                      onValueChange={(value) => updateFormData("category", value)}
                    >
                      <SelectTrigger className="rounded-xl h-12">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((cat) => (
                          <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {formData.category && (
                  <div className="space-y-2">
                    <Label htmlFor="subCategory">Sub-Category</Label>
                    <Select 
                      value={formData.subCategory} 
                      onValueChange={(value) => updateFormData("subCategory", value)}
                    >
                      <SelectTrigger className="rounded-xl h-12">
                        <SelectValue placeholder="Select sub-category" />
                      </SelectTrigger>
                      <SelectContent>
                        {subCategories[formData.category as keyof typeof subCategories]?.map((subCat) => (
                          <SelectItem key={subCat} value={subCat}>{subCat}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="description">Business Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your business, services, and what makes you unique..."
                    value={formData.description}
                    onChange={(e) => updateFormData("description", e.target.value)}
                    className="rounded-xl min-h-24"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="services">Services Offered</Label>
                  <Textarea
                    id="services"
                    placeholder="List the main services you provide..."
                    value={formData.services}
                    onChange={(e) => updateFormData("services", e.target.value)}
                    className="rounded-xl min-h-20"
                  />
                </div>
              </div>
            )}

            {/* Step 2: Gallery */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold">Gallery & Documents</h2>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Business Logo & Photos</Label>
                    <div className="border-2 border-dashed border-muted-foreground/20 rounded-xl p-8 text-center">
                      <Upload className="mx-auto w-10 h-10 text-muted-foreground mb-4" />
                      <h3 className="font-medium mb-2">Upload Business Images</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Add your logo, storefront, products, or service images
                      </p>
                      <Button variant="outline" className="rounded-lg">
                        Choose Images
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Business Documents</Label>
                    <div className="border-2 border-dashed border-muted-foreground/20 rounded-xl p-8 text-center">
                      <Upload className="mx-auto w-10 h-10 text-muted-foreground mb-4" />
                      <h3 className="font-medium mb-2">Upload Documents</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Business license, certificates, permits
                      </p>
                      <Button variant="outline" className="rounded-lg">
                        Choose Documents
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Location */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold">Business Location</h2>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="address">Street Address</Label>
                    <Input
                      id="address"
                      placeholder="123 Main Street"
                      value={formData.address}
                      onChange={(e) => updateFormData("address", e.target.value)}
                      className="rounded-xl h-12"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        placeholder="New York"
                        value={formData.city}
                        onChange={(e) => updateFormData("city", e.target.value)}
                        className="rounded-xl h-12"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        placeholder="NY"
                        value={formData.state}
                        onChange={(e) => updateFormData("state", e.target.value)}
                        className="rounded-xl h-12"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="zipCode">ZIP Code</Label>
                      <Input
                        id="zipCode"
                        placeholder="10001"
                        value={formData.zipCode}
                        onChange={(e) => updateFormData("zipCode", e.target.value)}
                        className="rounded-xl h-12"
                      />
                    </div>
                  </div>

                  <div className="bg-muted/30 rounded-xl p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Map Location</span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      Set your exact location on the map for better discoverability
                    </p>
                    <Button variant="outline" size="sm" className="rounded-lg">
                      Pin Location on Map
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Payment */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold">Joining Fee Payment</h2>
                
                <div className="bg-accent/10 rounded-2xl p-6 border-2 border-accent/20">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-accent">VendorHub Registration</h3>
                    <div className="text-3xl font-bold text-accent">$99</div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">
                    One-time fee to get your business listed and verified on our platform
                  </p>
                  <ul className="text-sm space-y-2">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      Business profile listing
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      Customer enquiry management
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      Analytics dashboard
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      24/7 customer support
                    </li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <Label>Payment Method</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border-2 border-primary rounded-xl p-4 cursor-pointer bg-primary/5">
                      <div className="flex items-center gap-3">
                        <CreditCard className="w-5 h-5 text-primary" />
                        <div>
                          <div className="font-medium">Credit/Debit Card</div>
                          <div className="text-sm text-muted-foreground">Visa, Mastercard, Amex</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={handlePayment}
                  disabled={isPaymentProcessing}
                  className="w-full h-12 rounded-xl bg-accent hover:bg-accent/90 text-white"
                >
                  {isPaymentProcessing ? "Processing Payment..." : "Pay $99 Now"}
                </Button>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex gap-4 pt-4">
              {currentStep > 1 && (
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(prev => prev - 1)}
                  className="flex-1 h-12 rounded-xl"
                >
                  Back
                </Button>
              )}
              
              {isRegistration && currentStep < 4 && (
                <Button
                  variant="ghost"
                  onClick={handleSkip}
                  className="h-12 rounded-xl text-muted-foreground hover:bg-muted"
                >
                  Skip for now
                </Button>
              )}
              
              {isRegistration && currentStep === 1 && (
                <Button
                  onClick={handleCompleteNow}
                  className="flex-1 h-12 rounded-xl bg-accent hover:bg-accent/90 text-white"
                >
                  Complete Later
                </Button>
              )}
              
              {!isRegistration || currentStep > 1 ? (
                <Button
                  onClick={handleNext}
                  disabled={
                    (currentStep === 1 && !formData.businessName) ||
                    (currentStep === 3 && !formData.address) ||
                    (currentStep === 4 && true) // Payment handles its own logic
                  }
                  className="flex-1 h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
                >
                  {currentStep === 4 ? "Submit for Approval" : "Next"}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  className="flex-1 h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
                >
                  Continue Setup
                </Button>
              )}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}