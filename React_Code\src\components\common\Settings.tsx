import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Separator } from "../ui/separator";
import { 
  User as UserIcon, 
  Bell, 
  Shield, 
  Moon, 
  Globe, 
  Smartphone,
  Mail,
  Save,
  Upload,
  Camera
} from "lucide-react";
import { User } from "../../App";

interface SettingsProps {
  user: User;
  onClose: () => void;
  onSave: (updatedUser: User) => void;
}

export function Settings({ user, onClose, onSave }: SettingsProps) {
  const [activeTab, setActiveTab] = useState("profile");
  const [formData, setFormData] = useState({
    name: user.name,
    email: user.email,
    phone: user.phone,
    businessName: user.businessName || "",
    avatar: user.avatar || "",
  });

  const [notifications, setNotifications] = useState({
    email: true,
    push: true,
    sms: false,
    marketing: false,
  });

  const [preferences, setPreferences] = useState({
    darkMode: false,
    language: "en",
    timezone: "UTC-5",
    currency: "USD",
  });

  const tabs = [
    { id: "profile", label: "Profile", icon: UserIcon },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "security", label: "Security", icon: Shield },
    { id: "preferences", label: "Preferences", icon: Globe },
  ];

  const handleSave = () => {
    const updatedUser: User = {
      ...user,
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      businessName: formData.businessName,
      avatar: formData.avatar,
    };
    onSave(updatedUser);
  };

  const renderProfileTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Profile Information</h3>
        <p className="text-sm text-muted-foreground">
          Update your personal information and how others see you on the platform.
        </p>
      </div>

      <Separator />

      {/* Avatar Section */}
      <div className="flex items-start gap-6">
        <div className="relative">
          <Avatar className="w-20 h-20">
            <AvatarFallback className="text-xl bg-primary text-white">
              {formData.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <Button
            size="sm"
            variant="outline"
            className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full p-0"
          >
            <Camera className="w-4 h-4" />
          </Button>
        </div>
        <div className="space-y-2">
          <h4 className="font-medium">Profile Photo</h4>
          <p className="text-sm text-muted-foreground">
            This will be displayed on your profile and in comments.
          </p>
          <Button variant="outline" size="sm" className="rounded-xl">
            <Upload className="w-4 h-4 mr-2" />
            Upload new photo
          </Button>
        </div>
      </div>

      <Separator />

      {/* Form Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="rounded-xl"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            className="rounded-xl"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            className="rounded-xl"
          />
        </div>

        {user.role === "vendor" && (
          <div className="space-y-2">
            <Label htmlFor="businessName">Business Name</Label>
            <Input
              id="businessName"
              value={formData.businessName}
              onChange={(e) => setFormData(prev => ({ ...prev, businessName: e.target.value }))}
              className="rounded-xl"
            />
          </div>
        )}
      </div>

      <Separator />

      <div className="space-y-2">
        <Label>Role</Label>
        <div className="p-3 bg-muted rounded-xl">
          <span className="font-medium capitalize">
            {user.role.replace('_', ' ')}
          </span>
        </div>
        <p className="text-sm text-muted-foreground">
          Contact support to change your account role.
        </p>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Notification Preferences</h3>
        <p className="text-sm text-muted-foreground">
          Choose how you'd like to be notified about activity on your account.
        </p>
      </div>

      <Separator />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Email Notifications</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Receive notifications via email about important updates
            </p>
          </div>
          <Switch
            checked={notifications.email}
            onCheckedChange={(checked) => 
              setNotifications(prev => ({ ...prev, email: checked }))
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Bell className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Push Notifications</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Get push notifications on your devices
            </p>
          </div>
          <Switch
            checked={notifications.push}
            onCheckedChange={(checked) => 
              setNotifications(prev => ({ ...prev, push: checked }))
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Smartphone className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">SMS Notifications</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Receive important alerts via SMS
            </p>
          </div>
          <Switch
            checked={notifications.sms}
            onCheckedChange={(checked) => 
              setNotifications(prev => ({ ...prev, sms: checked }))
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Globe className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Marketing Communications</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Receive updates about new features and promotions
            </p>
          </div>
          <Switch
            checked={notifications.marketing}
            onCheckedChange={(checked) => 
              setNotifications(prev => ({ ...prev, marketing: checked }))
            }
          />
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h4 className="font-medium">Notification Types</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">New enquiries</span>
            <Switch defaultChecked />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Payment updates</span>
            <Switch defaultChecked />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Review notifications</span>
            <Switch defaultChecked />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">System maintenance</span>
            <Switch defaultChecked />
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Security Settings</h3>
        <p className="text-sm text-muted-foreground">
          Manage your account security and authentication preferences.
        </p>
      </div>

      <Separator />

      <div className="space-y-6">
        <Card className="p-4 rounded-2xl border-green-200 bg-green-50">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-green-800">Two-Factor Authentication</h4>
              <p className="text-sm text-green-600">Your account is secured with 2FA</p>
            </div>
            <Button variant="outline" size="sm" className="rounded-xl">
              Manage
            </Button>
          </div>
        </Card>

        <div className="space-y-4">
          <h4 className="font-medium">Password</h4>
          <div className="space-y-3">
            <div className="space-y-2">
              <Label htmlFor="current-password">Current Password</Label>
              <Input
                id="current-password"
                type="password"
                placeholder="Enter current password"
                className="rounded-xl"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-password">New Password</Label>
              <Input
                id="new-password"
                type="password"
                placeholder="Enter new password"
                className="rounded-xl"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirm New Password</Label>
              <Input
                id="confirm-password"
                type="password"
                placeholder="Confirm new password"
                className="rounded-xl"
              />
            </div>
            <Button className="rounded-xl">
              Update Password
            </Button>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h4 className="font-medium">Login Activity</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-muted rounded-xl">
              <div>
                <div className="font-medium">Current Session</div>
                <div className="text-sm text-muted-foreground">New York, NY • Chrome on Mac</div>
              </div>
              <div className="text-sm text-green-600 font-medium">Active</div>
            </div>
            <div className="flex justify-between items-center p-3 bg-muted rounded-xl">
              <div>
                <div className="font-medium">Mobile App</div>
                <div className="text-sm text-muted-foreground">iPhone • 2 hours ago</div>
              </div>
              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                Revoke
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPreferencesTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Preferences</h3>
        <p className="text-sm text-muted-foreground">
          Customize your experience on VendorHub.
        </p>
      </div>

      <Separator />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Moon className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Dark Mode</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Use dark theme across the platform
            </p>
          </div>
          <Switch
            checked={preferences.darkMode}
            onCheckedChange={(checked) => 
              setPreferences(prev => ({ ...prev, darkMode: checked }))
            }
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="language">Language</Label>
            <Select 
              value={preferences.language} 
              onValueChange={(value) => 
                setPreferences(prev => ({ ...prev, language: value }))
              }
            >
              <SelectTrigger className="rounded-xl">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="timezone">Timezone</Label>
            <Select 
              value={preferences.timezone} 
              onValueChange={(value) => 
                setPreferences(prev => ({ ...prev, timezone: value }))
              }
            >
              <SelectTrigger className="rounded-xl">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="UTC-8">Pacific Time (UTC-8)</SelectItem>
                <SelectItem value="UTC-7">Mountain Time (UTC-7)</SelectItem>
                <SelectItem value="UTC-6">Central Time (UTC-6)</SelectItem>
                <SelectItem value="UTC-5">Eastern Time (UTC-5)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="currency">Currency</Label>
            <Select 
              value={preferences.currency} 
              onValueChange={(value) => 
                setPreferences(prev => ({ ...prev, currency: value }))
              }
            >
              <SelectTrigger className="rounded-xl">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USD">USD ($)</SelectItem>
                <SelectItem value="EUR">EUR (€)</SelectItem>
                <SelectItem value="GBP">GBP (£)</SelectItem>
                <SelectItem value="CAD">CAD ($)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h4 className="font-medium">Data & Privacy</h4>
          <div className="space-y-3">
            <Button variant="outline" className="w-full justify-start rounded-xl">
              Download Your Data
            </Button>
            <Button variant="outline" className="w-full justify-start rounded-xl">
              Export Account Information
            </Button>
            <Button variant="destructive" className="w-full justify-start rounded-xl">
              Delete Account
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCurrentTab = () => {
    switch (activeTab) {
      case "profile": return renderProfileTab();
      case "notifications": return renderNotificationsTab();
      case "security": return renderSecurityTab();
      case "preferences": return renderPreferencesTab();
      default: return renderProfileTab();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-64 bg-muted/30 border-r border-border p-6">
            <div className="space-y-2">
              <h2 className="text-xl font-bold mb-6">Settings</h2>
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-xl text-left transition-colors ${
                      activeTab === tab.id 
                        ? "bg-primary text-white" 
                        : "hover:bg-muted text-muted-foreground hover:text-foreground"
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 p-6 overflow-y-auto">
              {renderCurrentTab()}
            </div>

            {/* Footer */}
            <div className="border-t border-border p-6">
              <div className="flex gap-3 justify-end">
                <Button variant="outline" onClick={onClose} className="rounded-xl">
                  Cancel
                </Button>
                <Button onClick={handleSave} className="rounded-xl bg-primary hover:bg-primary/90">
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}