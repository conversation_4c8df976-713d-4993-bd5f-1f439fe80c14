import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Badge } from "../ui/badge";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { ImageWithFallback } from "../figma/ImageWithFallback";
import { 
  Building2, 
  Users, 
  Star, 
  TrendingUp, 
  Shield, 
  CheckCircle, 
  ArrowRight,
  Menu,
  X,
  Mail,
  Phone,
  MapPin,
  Globe,
  Zap,
  Target,
  BarChart3
} from "lucide-react";

interface LandingPageProps {
  onGetStarted: () => void;
  onLogin: () => void;
  onVendorOnboarding?: () => void;
}

export function LandingPage({ onGetStarted, onLogin, onVendorOnboarding }: LandingPageProps) {
  const [currentSection, setCurrentSection] = useState("home");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    setCurrentSection(sectionId);
    setIsMobileMenuOpen(false);
    
    // In a real app, you'd scroll to the section
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const navigationItems = [
    { id: "home", label: "Home" },
    { id: "about", label: "About" },
    { id: "pricing", label: "Pricing" },
    { id: "faq", label: "FAQ" },
    { id: "contact", label: "Contact" },
  ];

  const features = [
    {
      icon: Users,
      title: "Multi-Role Platform",
      description: "Seamlessly manage vendors, customers, and sales teams in one unified platform."
    },
    {
      icon: Shield,
      title: "Verified Vendors",
      description: "All vendors are thoroughly verified and approved by our admin team."
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Get detailed insights and reports to make data-driven business decisions."
    },
    {
      icon: Zap,
      title: "Instant Enquiries",
      description: "Connect customers with vendors instantly through our smart enquiry system."
    },
    {
      icon: Target,
      title: "Sales Management",
      description: "Track sales performance and commission with our comprehensive tools."
    },
    {
      icon: Globe,
      title: "Multi-Location",
      description: "Manage vendors and customers across multiple cities and regions."
    }
  ];

  const stats = [
    { number: "10K+", label: "Active Vendors" },
    { number: "50K+", label: "Happy Customers" },
    { number: "500+", label: "Cities Covered" },
    { number: "99.9%", label: "Uptime" }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Restaurant Owner",
      content: "VendorHub transformed how we connect with customers. Our enquiries increased by 300%!",
      rating: 5
    },
    {
      name: "Mike Chen",
      role: "Sales Executive",
      content: "The commission tracking and vendor onboarding tools are game-changers for our sales team.",
      rating: 5
    },
    {
      name: "Lisa Rodriguez",
      role: "Admin Manager",
      content: "Managing thousands of vendors has never been easier. The admin dashboard is incredibly powerful.",
      rating: 5
    }
  ];

  const pricingPlans = [
    {
      name: "Vendor Basic",
      price: "$99",
      period: "one-time",
      description: "Perfect for small businesses getting started",
      features: [
        "Business profile listing",
        "Customer enquiry management",
        "Basic analytics",
        "Email support"
      ],
      popular: false
    },
    {
      name: "Vendor Pro",
      price: "$199",
      period: "yearly",
      description: "Advanced features for growing businesses",
      features: [
        "Everything in Basic",
        "Advanced analytics",
        "Priority support",
        "Featured listings",
        "Custom branding"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "contact us",
      description: "Tailored solutions for large organizations",
      features: [
        "Everything in Pro",
        "API access",
        "Custom integrations",
        "Dedicated support",
        "White-label solution"
      ],
      popular: false
    }
  ];

  const faqs = [
    {
      question: "How do I get started as a vendor?",
      answer: "Simply click 'Get Started', create your account, complete the business verification process, and pay the one-time registration fee. Our team will review and approve your profile within 24-48 hours."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards, debit cards, and digital payment methods including PayPal, Stripe, and bank transfers."
    },
    {
      question: "How do customers find my business?",
      answer: "Customers can discover your business through our search functionality, category browsing, location-based results, and featured listings. We also optimize your profile for maximum visibility."
    },
    {
      question: "What commission do sales executives earn?",
      answer: "Sales executives earn a competitive 50% commission on every successful vendor onboarding. Payments are processed monthly with detailed tracking available in the dashboard."
    },
    {
      question: "Is there customer support available?",
      answer: "Yes! We provide 24/7 customer support through email, live chat, and phone. Enterprise customers get dedicated account managers."
    },
    {
      question: "Can I cancel my subscription anytime?",
      answer: "Yes, you can cancel your subscription anytime from your dashboard. For one-time vendor registrations, refunds are available within 30 days if you haven't received any enquiries."
    }
  ];

  const renderNavbar = () => (
    <nav className="fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-border z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary rounded-2xl flex items-center justify-center">
              <Building2 className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-foreground">VendorHub</span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-8">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => scrollToSection(item.id)}
                className={`text-sm font-medium transition-colors hover:text-primary ${
                  currentSection === item.id ? "text-primary" : "text-muted-foreground"
                }`}
              >
                {item.label}
              </button>
            ))}
          </div>

          {/* Desktop CTA */}
          <div className="hidden md:flex items-center gap-4">
            <Button variant="ghost" onClick={onLogin} className="rounded-xl">
              Login
            </Button>
            <Button onClick={onGetStarted} className="rounded-xl bg-primary hover:bg-primary/90">
              Get Started
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-2 rounded-xl hover:bg-muted"
          >
            {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <div className="space-y-4">
              {navigationItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`block w-full text-left px-2 py-2 text-sm font-medium transition-colors hover:text-primary ${
                    currentSection === item.id ? "text-primary" : "text-muted-foreground"
                  }`}
                >
                  {item.label}
                </button>
              ))}
              <div className="flex flex-col gap-2 pt-4">
                <Button variant="ghost" onClick={onLogin} className="rounded-xl">
                  Login
                </Button>
                <Button onClick={onGetStarted} className="rounded-xl bg-primary hover:bg-primary/90">
                  Get Started
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );

  const renderHeroSection = () => (
    <section id="home" className="pt-24 pb-12 bg-gradient-to-br from-primary/5 via-background to-accent/5">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge className="bg-primary/10 text-primary border-primary/20">
                🚀 Trusted by 10,000+ businesses
              </Badge>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                Connect with{" "}
                <span className="text-primary">Trusted Vendors</span>
                {" "}Effortlessly
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                The ultimate platform for vendors, customers, and sales teams to grow their business. 
                Join thousands of successful businesses already using VendorHub.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                onClick={onGetStarted}
                size="lg"
                className="h-14 px-8 rounded-2xl bg-primary hover:bg-primary/90 text-lg"
              >
                Start Free Trial
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => scrollToSection("about")}
                className="h-14 px-8 rounded-2xl text-lg"
              >
                Learn More
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-primary">{stat.number}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>

          <div className="relative">
            <div className="aspect-square rounded-3xl overflow-hidden shadow-2xl">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1579389248774-07907f421a6b?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBidXNpbmVzcyUyMHRlYW0lMjBjb2xsYWJvcmF0aW9ufGVufDF8fHx8MTc1Njg4Mjc0OXww&ixlib=rb-4.1.0&q=80&w=1080"
                alt="Modern business collaboration"
                className="w-full h-full object-cover"
              />
            </div>
            {/* Floating cards */}
            <Card className="absolute -top-4 -left-4 p-4 bg-white shadow-lg rounded-2xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <div className="font-semibold">Verified</div>
                  <div className="text-sm text-muted-foreground">100% Trusted</div>
                </div>
              </div>
            </Card>
            <Card className="absolute -bottom-4 -right-4 p-4 bg-white shadow-lg rounded-2xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold">300% Growth</div>
                  <div className="text-sm text-muted-foreground">Average increase</div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );

  const renderFeaturesSection = () => (
    <section id="about" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">Why Choose VendorHub?</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Everything you need to manage your vendor marketplace efficiently and scale your business
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="p-8 rounded-3xl border-0 shadow-sm hover:shadow-lg transition-shadow">
                <div className="space-y-4">
                  <div className="w-14 h-14 bg-primary/10 rounded-2xl flex items-center justify-center">
                    <Icon className="w-7 h-7 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold">{feature.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                </div>
              </Card>
            );
          })}
        </div>

        {/* About section */}
        <div className="mt-24 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-6">
            <h3 className="text-3xl font-bold">Built for Modern Businesses</h3>
            <p className="text-lg text-muted-foreground leading-relaxed">
              VendorHub is designed from the ground up to solve the challenges of modern vendor management. 
              Whether you're a small business owner, a growing enterprise, or a sales professional, 
              our platform provides the tools you need to succeed.
            </p>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Advanced vendor verification system</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Real-time analytics and reporting</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Multi-role access management</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>24/7 customer support</span>
              </div>
            </div>
          </div>
          <div className="relative">
            <div className="aspect-[4/3] rounded-3xl overflow-hidden shadow-2xl">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1718220216044-006f43e3a9b1?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBvZmZpY2UlMjB3b3Jrc3BhY2V8ZW58MXx8fHwxNzU2ODc2MDIyfDA&ixlib=rb-4.1.0&q=80&w=1080"
                alt="Modern office workspace"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );

  const renderTestimonialsSection = () => (
    <section className="py-24 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">What Our Users Say</h2>
          <p className="text-xl text-muted-foreground">
            Join thousands of satisfied businesses using VendorHub
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="p-8 rounded-3xl border-0 shadow-sm">
              <div className="space-y-6">
                <div className="flex items-center gap-1">
                  {Array.from({ length: testimonial.rating }, (_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <blockquote className="text-lg leading-relaxed">
                  "{testimonial.content}"
                </blockquote>
                <div>
                  <div className="font-semibold">{testimonial.name}</div>
                  <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );

  const renderPricingSection = () => (
    <section id="pricing" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">Simple, Transparent Pricing</h2>
          <p className="text-xl text-muted-foreground">
            Choose the plan that's right for your business
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => (
            <Card 
              key={index} 
              className={`p-8 rounded-3xl relative ${
                plan.popular 
                  ? "border-2 border-primary shadow-lg scale-105" 
                  : "border-0 shadow-sm"
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-white">
                  Most Popular
                </Badge>
              )}
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-2xl font-bold">{plan.name}</h3>
                  <p className="text-muted-foreground">{plan.description}</p>
                </div>
                
                <div>
                  <div className="text-4xl font-bold">
                    {plan.price}
                    {plan.period !== "contact us" && (
                      <span className="text-lg text-muted-foreground font-normal">
                        /{plan.period}
                      </span>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>

                <Button 
                  onClick={onGetStarted}
                  className={`w-full h-12 rounded-2xl ${
                    plan.popular 
                      ? "bg-primary hover:bg-primary/90" 
                      : "bg-secondary text-foreground hover:bg-secondary/80"
                  }`}
                >
                  {plan.price === "Custom" ? "Contact Sales" : "Get Started"}
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );

  const renderFAQSection = () => (
    <section id="faq" className="py-24 bg-muted/30">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">Frequently Asked Questions</h2>
          <p className="text-xl text-muted-foreground">
            Find answers to common questions about VendorHub
          </p>
        </div>

        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <Card key={index} className="p-6 rounded-2xl border-0 shadow-sm">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">{faq.question}</h3>
                <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );

  const renderContactSection = () => (
    <section id="contact" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">Get in Touch</h2>
          <p className="text-xl text-muted-foreground">
            Have questions? We'd love to hear from you
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          <div className="space-y-8">
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center">
                  <Mail className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold">Email Us</h3>
                  <p className="text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center">
                  <Phone className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold">Call Us</h3>
                  <p className="text-muted-foreground">+****************</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center">
                  <MapPin className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold">Visit Us</h3>
                  <p className="text-muted-foreground">123 Business Ave, Suite 100<br />New York, NY 10001</p>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-video rounded-3xl overflow-hidden shadow-lg">
                <ImageWithFallback
                  src="https://images.unsplash.com/photo-1664575600397-88e370cb46b8?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxzbWFsbCUyMGJ1c2luZXNzJTIwb3duZXJ8ZW58MXx8fHwxNzU2ODk4NzkwfDA&ixlib=rb-4.1.0&q=80&w=1080"
                  alt="Small business owner"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>

          <Card className="p-8 rounded-3xl border-0 shadow-lg">
            <div className="space-y-6">
              <h3 className="text-2xl font-bold">Send us a message</h3>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">First Name</label>
                    <Input placeholder="John" className="rounded-xl" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Last Name</label>
                    <Input placeholder="Doe" className="rounded-xl" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email</label>
                  <Input type="email" placeholder="<EMAIL>" className="rounded-xl" />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Subject</label>
                  <Input placeholder="How can we help?" className="rounded-xl" />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Message</label>
                  <Textarea 
                    placeholder="Tell us more about your inquiry..." 
                    className="rounded-xl min-h-32" 
                  />
                </div>
              </div>

              <Button className="w-full h-12 rounded-2xl bg-primary hover:bg-primary/90">
                Send Message
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );

  const renderFooter = () => (
    <footer className="bg-foreground text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-2xl flex items-center justify-center">
                <Building2 className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold">VendorHub</span>
            </div>
            <p className="text-gray-400">
              The ultimate platform for vendors, customers, and sales teams to grow their business.
            </p>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-semibold">Product</h4>
            <div className="space-y-2 text-gray-400">
              <div>Features</div>
              <div>Pricing</div>
              <div>API</div>
              <div>Documentation</div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-semibold">Company</h4>
            <div className="space-y-2 text-gray-400">
              <div>About Us</div>
              <div>Careers</div>
              <div>Blog</div>
              <div>Press</div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-semibold">Support</h4>
            <div className="space-y-2 text-gray-400">
              <div>Help Center</div>
              <div>Contact Us</div>
              <div>Privacy Policy</div>
              <div>Terms of Service</div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
          <p>&copy; 2025 VendorHub. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );

  return (
    <div className="min-h-screen bg-background">
      {renderNavbar()}
      {renderHeroSection()}
      {renderFeaturesSection()}
      {renderTestimonialsSection()}
      {renderPricingSection()}
      {renderFAQSection()}
      {renderContactSection()}
      {renderFooter()}
    </div>
  );
}