import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Badge } from "../ui/badge";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { 
  BarChart3, 
  Eye, 
  MessageSquare, 
  Star, 
  Calendar, 
  CreditCard,
  Settings,
  Upload,
  MapPin,
  Clock,
  Phone,
  Mail,
  Globe,
  TrendingUp,
  DollarSign,
  Users,
  FileText
} from "lucide-react";
import { User as UserType } from "../../App";
import { MobileBottomNav } from "../layout/MobileBottomNav";
import { DesktopSidebar } from "../layout/DesktopSidebar";
import { ChartCard, VendorPerformance<PERSON>hart, Enquiry<PERSON>hart, RevenueChart } from "../charts/DashboardCharts";

interface VendorDashboardProps {
  user: UserType;
  onLogout: () => void;
}

export function VendorDashboard({ user, onLogout }: VendorDashboardProps) {
  const [currentView, setCurrentView] = useState("dashboard");

  // Mock data
  const businessStats = {
    views: 1240,
    enquiries: 45,
    reviews: 23,
    rating: 4.6,
  };

  const recentEnquiries = [
    {
      id: 1,
      customer: "John Smith",
      message: "Looking for catering services for 50 people event",
      date: "2 hours ago",
      status: "new",
    },
    {
      id: 2,
      customer: "<PERSON> <PERSON>",
      message: "Need website development for small business",
      date: "1 day ago",
      status: "contacted",
    },
    {
      id: 3,
      customer: "Mike Brown",
      message: "Interested in your repair services",
      date: "3 days ago",
      status: "closed",
    },
  ];

  const reviews = [
    {
      id: 1,
      customer: "Alice Wilson",
      rating: 5,
      comment: "Excellent service! Very professional and timely.",
      date: "1 week ago",
    },
    {
      id: 2,
      customer: "Bob Davis",
      rating: 4,
      comment: "Good quality work, would recommend.",
      date: "2 weeks ago",
    },
  ];

  const businessProfile = {
    name: "Giuseppe's Italian Kitchen",
    category: "Restaurants & Food",
    description: "Authentic Italian cuisine with fresh ingredients and traditional recipes passed down through generations.",
    services: "Dine-in, Takeout, Catering, Private Events",
    address: "123 Main Street, New York, NY 10001",
    phone: "+****************",
    email: "<EMAIL>",
    website: "www.giuseppes.com",
    hours: {
      monday: "9:00 AM - 9:00 PM",
      tuesday: "9:00 AM - 9:00 PM",
      wednesday: "9:00 AM - 9:00 PM",
      thursday: "9:00 AM - 9:00 PM",
      friday: "9:00 AM - 10:00 PM",
      saturday: "10:00 AM - 10:00 PM",
      sunday: "10:00 AM - 9:00 PM",
    },
  };

  const renderDashboardView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's your business overview.</p>
        </div>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          <TrendingUp className="w-4 h-4 mr-2" />
          View Analytics
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Eye className="w-5 h-5 text-primary" />
              <span className="text-sm text-green-600 font-medium">+12%</span>
            </div>
            <div className="text-2xl font-bold">{businessStats.views}</div>
            <div className="text-sm text-muted-foreground">Profile Views</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <MessageSquare className="w-5 h-5 text-accent" />
              <span className="text-sm text-green-600 font-medium">+8%</span>
            </div>
            <div className="text-2xl font-bold">{businessStats.enquiries}</div>
            <div className="text-sm text-muted-foreground">Enquiries</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Star className="w-5 h-5 text-yellow-500" />
              <span className="text-sm text-green-600 font-medium">+0.2</span>
            </div>
            <div className="text-2xl font-bold">{businessStats.rating}</div>
            <div className="text-sm text-muted-foreground">Rating</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="text-sm text-green-600 font-medium">+15%</span>
            </div>
            <div className="text-2xl font-bold">{businessStats.reviews}</div>
            <div className="text-sm text-muted-foreground">Reviews</div>
          </div>
        </Card>
      </div>

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard title="Performance Metrics" subtitle="Your business performance overview">
          <VendorPerformanceChart />
        </ChartCard>
        
        <ChartCard title="Enquiry Trends" subtitle="Weekly enquiry and response activity">
          <EnquiryChart />
        </ChartCard>
      </div>

      {/* Recent Enquiries */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 rounded-2xl">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Enquiries</h3>
              <Button variant="ghost" size="sm" onClick={() => setCurrentView("enquiries")}>
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {recentEnquiries.slice(0, 3).map((enquiry) => (
                <div key={enquiry.id} className="flex items-start gap-3 p-3 bg-muted/30 rounded-xl">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="text-xs">
                      {enquiry.customer.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-sm">{enquiry.customer}</span>
                      <Badge 
                        variant={enquiry.status === "new" ? "default" : "secondary"}
                        className={`text-xs ${
                          enquiry.status === "new" 
                            ? "bg-accent text-white" 
                            : enquiry.status === "contacted"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        {enquiry.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">
                      {enquiry.message}
                    </p>
                    <span className="text-xs text-muted-foreground">{enquiry.date}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Reviews</h3>
              <Button variant="ghost" size="sm" onClick={() => setCurrentView("reviews")}>
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {reviews.map((review) => (
                <div key={review.id} className="p-3 bg-muted/30 rounded-xl">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-sm">{review.customer}</span>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }, (_, i) => (
                        <Star 
                          key={i} 
                          className={`w-3 h-3 ${
                            i < review.rating 
                              ? "fill-yellow-400 text-yellow-400" 
                              : "text-muted-foreground"
                          }`} 
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-1">{review.comment}</p>
                  <span className="text-xs text-muted-foreground">{review.date}</span>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderProfileView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Business Profile</h1>
        <Button variant="outline" className="rounded-xl">
          <Upload className="w-4 h-4 mr-2" />
          Add Photos
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Business Name</label>
              <Input 
                value={businessProfile.name} 
                className="rounded-xl" 
                readOnly
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Category</label>
              <Input 
                value={businessProfile.category} 
                className="rounded-xl" 
                readOnly
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea 
                value={businessProfile.description} 
                className="rounded-xl min-h-20" 
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Services</label>
              <Textarea 
                value={businessProfile.services} 
                className="rounded-xl" 
              />
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Address</label>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-muted-foreground" />
                <Input 
                  value={businessProfile.address} 
                  className="rounded-xl" 
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Phone</label>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-muted-foreground" />
                <Input 
                  value={businessProfile.phone} 
                  className="rounded-xl" 
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Email</label>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <Input 
                  value={businessProfile.email} 
                  className="rounded-xl" 
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Website</label>
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-muted-foreground" />
                <Input 
                  value={businessProfile.website} 
                  className="rounded-xl" 
                />
              </div>
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Operating Hours</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(businessProfile.hours).map(([day, hours]) => (
            <div key={day} className="flex items-center justify-between p-3 bg-muted/30 rounded-xl">
              <span className="font-medium capitalize">{day}</span>
              <span className="text-sm text-muted-foreground">{hours}</span>
            </div>
          ))}
        </div>
      </Card>

      <div className="flex gap-4">
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          Save Changes
        </Button>
        <Button variant="outline" className="rounded-xl">
          Preview Profile
        </Button>
      </div>
    </div>
  );

  const renderEnquiriesView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Enquiries</h1>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="rounded-xl">
            Filter
          </Button>
          <Button variant="outline" size="sm" className="rounded-xl">
            Sort
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        {recentEnquiries.map((enquiry) => (
          <Card key={enquiry.id} className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {enquiry.customer.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{enquiry.customer}</h3>
                    <p className="text-muted-foreground">{enquiry.date}</p>
                  </div>
                </div>
                <Badge 
                  variant={enquiry.status === "new" ? "default" : "secondary"}
                  className={
                    enquiry.status === "new" 
                      ? "bg-accent text-white" 
                      : enquiry.status === "contacted"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-green-100 text-green-800"
                  }
                >
                  {enquiry.status}
                </Badge>
              </div>
              
              <p className="text-foreground">{enquiry.message}</p>
              
              <div className="flex gap-2">
                <Button size="sm" className="rounded-lg bg-primary hover:bg-primary/90">
                  Respond
                </Button>
                <Button variant="outline" size="sm" className="rounded-lg">
                  Mark as Contacted
                </Button>
                {enquiry.status === "new" && (
                  <Button variant="ghost" size="sm" className="rounded-lg text-muted-foreground">
                    Archive
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderPaymentsView = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Payments & Subscription</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Current Plan</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span>VendorHub Basic</span>
              <Badge className="bg-green-100 text-green-800">Active</Badge>
            </div>
            <div className="text-2xl font-bold text-accent">$99/year</div>
            <p className="text-sm text-muted-foreground">
              Next billing date: March 15, 2025
            </p>
            <Button variant="outline" className="w-full rounded-xl">
              Upgrade Plan
            </Button>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Payment History</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-muted/30 rounded-xl">
              <div>
                <div className="font-medium">Annual Subscription</div>
                <div className="text-sm text-muted-foreground">Mar 15, 2024</div>
              </div>
              <div className="text-accent font-semibold">$99</div>
            </div>
            <div className="flex justify-between items-center p-3 bg-muted/30 rounded-xl">
              <div>
                <div className="font-medium">Setup Fee</div>
                <div className="text-sm text-muted-foreground">Mar 15, 2024</div>
              </div>
              <div className="text-accent font-semibold">$50</div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderReviewsView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Reviews & Ratings</h1>
        <div className="text-right">
          <div className="text-3xl font-bold text-accent">{businessStats.rating}</div>
          <div className="flex items-center gap-1">
            {Array.from({ length: 5 }, (_, i) => (
              <Star 
                key={i} 
                className={`w-4 h-4 ${
                  i < Math.floor(businessStats.rating) 
                    ? "fill-yellow-400 text-yellow-400" 
                    : "text-muted-foreground"
                }`} 
              />
            ))}
          </div>
          <div className="text-sm text-muted-foreground">
            {businessStats.reviews} reviews
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id} className="p-6 rounded-2xl">
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {review.customer.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{review.customer}</h3>
                    <div className="flex items-center gap-1 mt-1">
                      {Array.from({ length: 5 }, (_, i) => (
                        <Star 
                          key={i} 
                          className={`w-4 h-4 ${
                            i < review.rating 
                              ? "fill-yellow-400 text-yellow-400" 
                              : "text-muted-foreground"
                          }`} 
                        />
                      ))}
                    </div>
                  </div>
                </div>
                <span className="text-sm text-muted-foreground">{review.date}</span>
              </div>
              
              <p className="text-foreground">{review.comment}</p>
              
              <Button variant="outline" size="sm" className="rounded-lg">
                Respond
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );



  const renderReportsView = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Business Reports</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard title="Revenue Trends" subtitle="Monthly revenue performance">
          <RevenueChart />
        </ChartCard>
        
        <ChartCard title="Business Growth" subtitle="Key performance indicators">
          <VendorPerformanceChart />
        </ChartCard>
      </div>

      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-muted/30 rounded-xl">
            <div className="text-2xl font-bold text-primary">87%</div>
            <div className="text-sm text-muted-foreground">Response Rate</div>
          </div>
          <div className="text-center p-4 bg-muted/30 rounded-xl">
            <div className="text-2xl font-bold text-green-600">2.3h</div>
            <div className="text-sm text-muted-foreground">Avg Response Time</div>
          </div>
          <div className="text-center p-4 bg-muted/30 rounded-xl">
            <div className="text-2xl font-bold text-accent">34%</div>
            <div className="text-sm text-muted-foreground">Conversion Rate</div>
          </div>
          <div className="text-center p-4 bg-muted/30 rounded-xl">
            <div className="text-2xl font-bold text-blue-600">95%</div>
            <div className="text-sm text-muted-foreground">Customer Satisfaction</div>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case "dashboard": return renderDashboardView();
      case "profile": return renderProfileView();
      case "enquiries": return renderEnquiriesView();
      case "payments": return renderPaymentsView();
      case "reviews": return renderReviewsView();
      case "reports": return renderReportsView();
      default: return renderDashboardView();
    }
  };

  const navigationItems = [
    { id: "dashboard", label: "Dashboard", icon: BarChart3 },
    { id: "profile", label: "Profile", icon: Settings },
    { id: "enquiries", label: "Enquiries", icon: MessageSquare },
    { id: "payments", label: "Payments", icon: CreditCard },
    { id: "reviews", label: "Reviews", icon: Star },
    { id: "reports", label: "Reports", icon: FileText },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Desktop Layout */}
      <div className="hidden md:flex h-screen">
        <DesktopSidebar
          user={user}
          navigationItems={navigationItems}
          currentView={currentView}
          onNavigate={setCurrentView}
          onLogout={onLogout}
        />
        <main className="flex-1 overflow-y-auto">
          <div className="p-6 max-w-7xl mx-auto">
            {renderCurrentView()}
          </div>
        </main>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden">
        <main className="pb-20">
          <div className="p-4">
            {renderCurrentView()}
          </div>
        </main>
        
        <MobileBottomNav
          navigationItems={navigationItems}
          currentView={currentView}
          onNavigate={setCurrentView}
        />
      </div>
    </div>
  );
}