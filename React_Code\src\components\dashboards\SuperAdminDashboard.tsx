import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Badge } from "../ui/badge";
import { Input } from "../ui/input";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { 
  BarChart3, 
  Users, 
  Building2, 
  DollarSign, 
  TrendingUp,
  Settings,
  UserPlus,
  FileText,
  Shield,
  Download,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";
import { User as UserType } from "../../App";
import { MobileBottomNav } from "../layout/MobileBottomNav";
import { DesktopSidebar } from "../layout/DesktopSidebar";

interface SuperAdminDashboardProps {
  user: UserType;
  onLogout: () => void;
}

export function SuperAdminDashboard({ user, onLogout }: SuperAdminDashboardProps) {
  const [currentView, setCurrentView] = useState("dashboard");

  // Mock data
  const systemStats = {
    totalVendors: 2847,
    totalCustomers: 8934,
    totalSalesExecs: 45,
    totalRevenue: 284750,
    monthlyGrowth: {
      vendors: 12.5,
      customers: 18.2,
      revenue: 15.8,
    },
  };

  const recentActivities = [
    {
      id: 1,
      type: "vendor_registered",
      message: "New vendor 'TechFix Solutions' registered",
      time: "2 hours ago",
      status: "pending_approval"
    },
    {
      id: 2,
      type: "payment_received",
      message: "$99 payment received from 'Giuseppe's Kitchen'",
      time: "4 hours ago",
      status: "completed"
    },
    {
      id: 3,
      type: "admin_created",
      message: "New admin 'Sarah Wilson' added to system",
      time: "1 day ago",
      status: "completed"
    },
  ];

  const pendingApprovals = [
    {
      id: 1,
      vendor: "TechFix Solutions",
      category: "Technology",
      registeredBy: "Mike Johnson (Sales Exec)",
      date: "2024-01-15",
      status: "pending",
    },
    {
      id: 2,
      vendor: "Green Garden Landscaping",
      category: "Services",
      registeredBy: "Lisa Brown (Sales Exec)",
      date: "2024-01-14",
      status: "pending",
    },
  ];

  const systemUsers = [
    {
      id: 1,
      name: "John Smith",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      lastLogin: "2 hours ago",
    },
    {
      id: 2,
      name: "Sarah Wilson",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      lastLogin: "1 day ago",
    },
    {
      id: 3,
      name: "Mike Johnson",
      email: "<EMAIL>",
      role: "sales_executive",
      status: "active",
      lastLogin: "3 hours ago",
    },
    {
      id: 4,
      name: "Lisa Brown",
      email: "<EMAIL>",
      role: "sales_executive",
      status: "inactive",
      lastLogin: "1 week ago",
    },
  ];

  const renderDashboardView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">System Overview</h1>
          <p className="text-muted-foreground">Monitor platform performance and manage system-wide operations</p>
        </div>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          <Download className="w-4 h-4 mr-2" />
          Generate Report
        </Button>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Building2 className="w-5 h-5 text-primary" />
              <span className="text-sm text-green-600 font-medium">
                +{systemStats.monthlyGrowth.vendors}%
              </span>
            </div>
            <div className="text-2xl font-bold">{systemStats.totalVendors.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Total Vendors</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="text-sm text-green-600 font-medium">
                +{systemStats.monthlyGrowth.customers}%
              </span>
            </div>
            <div className="text-2xl font-bold">{systemStats.totalCustomers.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Total Customers</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Shield className="w-5 h-5 text-accent" />
              <span className="text-sm text-muted-foreground">Active</span>
            </div>
            <div className="text-2xl font-bold">{systemStats.totalSalesExecs}</div>
            <div className="text-sm text-muted-foreground">Sales Executives</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <DollarSign className="w-5 h-5 text-green-500" />
              <span className="text-sm text-green-600 font-medium">
                +{systemStats.monthlyGrowth.revenue}%
              </span>
            </div>
            <div className="text-2xl font-bold">
              ${(systemStats.totalRevenue / 1000).toFixed(0)}K
            </div>
            <div className="text-sm text-muted-foreground">Total Revenue</div>
          </div>
        </Card>
      </div>

      {/* Recent Activities & Pending Approvals */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 rounded-2xl">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Activities</h3>
              <Button variant="ghost" size="sm">View All</Button>
            </div>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-3 bg-muted/30 rounded-xl">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    activity.status === "completed" ? "bg-green-500" : 
                    activity.status === "pending_approval" ? "bg-yellow-500" : "bg-blue-500"
                  }`} />
                  <div className="flex-1">
                    <p className="text-sm font-medium">{activity.message}</p>
                    <span className="text-xs text-muted-foreground">{activity.time}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Pending Approvals</h3>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                {pendingApprovals.length} pending
              </Badge>
            </div>
            <div className="space-y-4">
              {pendingApprovals.map((vendor) => (
                <div key={vendor.id} className="p-3 bg-muted/30 rounded-xl">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-semibold text-sm">{vendor.vendor}</h4>
                      <p className="text-xs text-muted-foreground">{vendor.category}</p>
                      <p className="text-xs text-muted-foreground">By {vendor.registeredBy}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      {vendor.date}
                    </Badge>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" className="h-7 text-xs rounded-lg bg-green-600 hover:bg-green-700">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Approve
                    </Button>
                    <Button variant="outline" size="sm" className="h-7 text-xs rounded-lg">
                      <XCircle className="w-3 h-3 mr-1" />
                      Reject
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderUsersView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">User Management</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="rounded-xl">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button className="rounded-xl bg-primary hover:bg-primary/90">
            <UserPlus className="w-4 h-4 mr-2" />
            Add User
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search users..."
            className="pl-10 rounded-xl"
          />
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-40 rounded-xl">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="admin">Admins</SelectItem>
            <SelectItem value="sales_executive">Sales Executives</SelectItem>
            <SelectItem value="vendor">Vendors</SelectItem>
            <SelectItem value="customer">Customers</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="sm" className="rounded-xl">
          <Filter className="w-4 h-4" />
        </Button>
      </div>

      {/* Users Table */}
      <Card className="rounded-2xl overflow-hidden">
        <div className="p-6">
          <div className="space-y-4">
            {systemUsers.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 bg-muted/30 rounded-xl">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {user.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold">{user.name}</div>
                    <div className="text-sm text-muted-foreground">{user.email}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <Badge 
                      variant={user.role === "admin" ? "default" : "secondary"}
                      className={`capitalize ${
                        user.role === "admin" ? "bg-primary text-white" : 
                        user.role === "sales_executive" ? "bg-blue-100 text-blue-800" : ""
                      }`}
                    >
                      {user.role.replace('_', ' ')}
                    </Badge>
                    <div className="text-xs text-muted-foreground mt-1">
                      Last login: {user.lastLogin}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={user.status === "active" ? "default" : "secondary"}
                      className={user.status === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                    >
                      {user.status}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );

  const renderReportsView = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Reports & Analytics</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="p-6 rounded-2xl cursor-pointer hover:shadow-lg transition-shadow">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold">Platform Performance</h3>
                <p className="text-sm text-muted-foreground">User engagement metrics</p>
              </div>
            </div>
            <Button variant="outline" className="w-full rounded-xl">
              <Download className="w-4 h-4 mr-2" />
              Download Report
            </Button>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl cursor-pointer hover:shadow-lg transition-shadow">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold">Revenue Report</h3>
                <p className="text-sm text-muted-foreground">Financial performance</p>
              </div>
            </div>
            <Button variant="outline" className="w-full rounded-xl">
              <Download className="w-4 h-4 mr-2" />
              Download Report
            </Button>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl cursor-pointer hover:shadow-lg transition-shadow">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold">User Activity</h3>
                <p className="text-sm text-muted-foreground">Registration & usage stats</p>
              </div>
            </div>
            <Button variant="outline" className="w-full rounded-xl">
              <Download className="w-4 h-4 mr-2" />
              Download Report
            </Button>
          </div>
        </Card>
      </div>

      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-muted/30 rounded-xl">
            <div className="text-2xl font-bold text-primary">847</div>
            <div className="text-sm text-muted-foreground">New Vendors This Month</div>
          </div>
          <div className="text-center p-4 bg-muted/30 rounded-xl">
            <div className="text-2xl font-bold text-blue-600">2.1K</div>
            <div className="text-sm text-muted-foreground">New Customers This Month</div>
          </div>
          <div className="text-center p-4 bg-muted/30 rounded-xl">
            <div className="text-2xl font-bold text-green-600">$48.2K</div>
            <div className="text-sm text-muted-foreground">Revenue This Month</div>
          </div>
          <div className="text-center p-4 bg-muted/30 rounded-xl">
            <div className="text-2xl font-bold text-accent">98.5%</div>
            <div className="text-sm text-muted-foreground">System Uptime</div>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderSettingsView = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">System Settings</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Platform Configuration</h3>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Platform Name</label>
              <Input value="VendorHub" className="rounded-xl" />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Support Email</label>
              <Input value="<EMAIL>" className="rounded-xl" />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Vendor Registration Fee</label>
              <Input value="$99" className="rounded-xl" />
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Notification Settings</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">New User Registrations</span>
              <Button variant="outline" size="sm" className="rounded-lg">
                Configure
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Payment Notifications</span>
              <Button variant="outline" size="sm" className="rounded-lg">
                Configure
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">System Alerts</span>
              <Button variant="outline" size="sm" className="rounded-lg">
                Configure
              </Button>
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Integration Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Payment Gateway</label>
              <Select defaultValue="stripe">
                <SelectTrigger className="rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="stripe">Stripe</SelectItem>
                  <SelectItem value="paypal">PayPal</SelectItem>
                  <SelectItem value="razorpay">Razorpay</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">SMS Provider</label>
              <Select defaultValue="twilio">
                <SelectTrigger className="rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="twilio">Twilio</SelectItem>
                  <SelectItem value="aws">AWS SNS</SelectItem>
                  <SelectItem value="messagebird">MessageBird</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Email Provider</label>
              <Select defaultValue="sendgrid">
                <SelectTrigger className="rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sendgrid">SendGrid</SelectItem>
                  <SelectItem value="mailgun">Mailgun</SelectItem>
                  <SelectItem value="aws-ses">AWS SES</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Maps Provider</label>
              <Select defaultValue="google">
                <SelectTrigger className="rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="google">Google Maps</SelectItem>
                  <SelectItem value="mapbox">Mapbox</SelectItem>
                  <SelectItem value="openstreet">OpenStreetMap</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </Card>

      <div className="flex gap-4">
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          Save Settings
        </Button>
        <Button variant="outline" className="rounded-xl">
          Reset to Defaults
        </Button>
      </div>
    </div>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case "dashboard": return renderDashboardView();
      case "users": return renderUsersView();
      case "reports": return renderReportsView();
      case "settings": return renderSettingsView();
      default: return renderDashboardView();
    }
  };

  const navigationItems = [
    { id: "dashboard", label: "Dashboard", icon: BarChart3 },
    { id: "users", label: "User Management", icon: Users },
    { id: "reports", label: "Reports", icon: FileText },
    { id: "settings", label: "Settings", icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Desktop Layout */}
      <div className="hidden md:flex h-screen">
        <DesktopSidebar
          user={user}
          navigationItems={navigationItems}
          currentView={currentView}
          onNavigate={setCurrentView}
          onLogout={onLogout}
        />
        <main className="flex-1 overflow-y-auto">
          <div className="p-6 max-w-7xl mx-auto">
            {renderCurrentView()}
          </div>
        </main>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden">
        <main className="pb-20">
          <div className="p-4">
            {renderCurrentView()}
          </div>
        </main>
        
        <MobileBottomNav
          navigationItems={navigationItems}
          currentView={currentView}
          onNavigate={setCurrentView}
        />
      </div>
    </div>
  );
}