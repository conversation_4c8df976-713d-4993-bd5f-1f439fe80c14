import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { Badge } from "../ui/badge";
import { Building2, LogOut, Settings as SettingsIcon, Bell, LucideIcon } from "lucide-react";
import { User } from "../../App";
import { Settings } from "../common/Settings";
import { Notifications } from "../common/Notifications";

interface NavigationItem {
  id: string;
  label: string;
  icon: LucideIcon;
}

interface DesktopSidebarProps {
  user: User;
  navigationItems: NavigationItem[];
  currentView: string;
  onNavigate: (view: string) => void;
  onLogout: () => void;
  onUserUpdate?: (user: User) => void;
}

export function DesktopSidebar({ 
  user, 
  navigationItems, 
  currentView, 
  onNavigate, 
  onLogout,
  onUserUpdate
}: DesktopSidebarProps) {
  const [showSettings, setShowSettings] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  const handleUserUpdate = (updatedUser: User) => {
    if (onUserUpdate) {
      onUserUpdate(updatedUser);
    }
    setShowSettings(false);
  };
  return (
    <div className="w-64 bg-white border-r border-border flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-primary rounded-2xl flex items-center justify-center">
            <Building2 className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="font-bold text-lg text-foreground">VendorHub</h1>
            <p className="text-xs text-muted-foreground capitalize">
              {user.role.replace('_', ' ')} Portal
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 p-4">
        <nav className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;
            
            return (
              <Button
                key={item.id}
                variant={isActive ? "default" : "ghost"}
                size="sm"
                onClick={() => onNavigate(item.id)}
                className={`w-full justify-start h-10 rounded-xl ${
                  isActive 
                    ? "bg-primary text-white hover:bg-primary/90" 
                    : "text-muted-foreground hover:text-foreground hover:bg-muted"
                }`}
              >
                <Icon className="w-4 h-4 mr-3" />
                {item.label}
              </Button>
            );
          })}
        </nav>
      </div>

      {/* User Profile */}
      <div className="p-4 border-t border-border">
        <div className="flex items-center gap-3 mb-4">
          <Avatar className="w-10 h-10">
            <AvatarFallback className="bg-primary text-white text-sm">
              {user.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="font-medium text-sm">{user.name}</div>
            <div className="text-xs text-muted-foreground">{user.email}</div>
          </div>
        </div>
        
        <div className="space-y-2">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowNotifications(true)}
            className="w-full justify-start h-9 rounded-xl text-muted-foreground hover:text-foreground"
          >
            <Bell className="w-4 h-4 mr-3" />
            Notifications
            <Badge className="ml-auto bg-primary text-white text-xs">3</Badge>
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowSettings(true)}
            className="w-full justify-start h-9 rounded-xl text-muted-foreground hover:text-foreground"
          >
            <SettingsIcon className="w-4 h-4 mr-3" />
            Settings
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onLogout}
            className="w-full justify-start h-9 rounded-xl text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <LogOut className="w-4 h-4 mr-3" />
            Logout
          </Button>
        </div>
      </div>

      {/* Settings Modal */}
      {showSettings && (
        <Settings
          user={user}
          onClose={() => setShowSettings(false)}
          onSave={handleUserUpdate}
        />
      )}

      {/* Notifications Modal */}
      {showNotifications && (
        <Notifications
          user={user}
          onClose={() => setShowNotifications(false)}
        />
      )}
    </div>
  );
}