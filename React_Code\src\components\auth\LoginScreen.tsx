import { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Card } from "../ui/card";
import { Building2, ArrowLeft, Mail, Phone } from "lucide-react";
import { UserRole } from "../../App";

interface LoginScreenProps {
  onLogin: (phone: string, email: string, role: UserRole) => void;
  onRegister: () => void;
}

export function LoginScreen({ onLogin, onRegister }: LoginScreenProps) {
  const [contactMethod, setContactMethod] = useState<"phone" | "email">("phone");
  const [phoneOrEmail, setPhoneOrEmail] = useState("");
  const [selectedRole, setSelectedRole] = useState<UserRole>("customer");
  const [isLoading, setIsLoading] = useState(false);

  const handleSendOTP = () => {
    if (!phoneOrEmail.trim()) return;
    
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      setIsLoading(false);
      const phone = contactMethod === "phone" ? phoneOrEmail : "";
      const email = contactMethod === "email" ? phoneOrEmail : "";
      onLogin(phone, email, selectedRole);
    }, 1000);
  };

  const roleOptions = [
    { value: "customer", label: "Customer", description: "Find and connect with vendors" },
    { value: "vendor", label: "Vendor", description: "List your business and services" },
    { value: "sales_executive", label: "Sales Executive", description: "Manage vendor onboarding" },
    { value: "admin", label: "Admin", description: "Platform administration" },
    { value: "super_admin", label: "Super Admin", description: "Full system access" },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-primary rounded-2xl flex items-center justify-center shadow-lg">
            <Building2 className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Welcome back</h1>
            <p className="text-muted-foreground">Sign in to your account</p>
          </div>
        </div>

        {/* Login Form */}
        <Card className="p-6 rounded-2xl border shadow-lg">
          <div className="space-y-6">
            {/* Contact Method Toggle */}
            <div className="flex rounded-xl bg-muted p-1">
              <button
                onClick={() => setContactMethod("phone")}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg transition-colors ${
                  contactMethod === "phone"
                    ? "bg-white shadow-sm text-foreground"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                <Phone className="w-4 h-4" />
                Phone
              </button>
              <button
                onClick={() => setContactMethod("email")}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg transition-colors ${
                  contactMethod === "email"
                    ? "bg-white shadow-sm text-foreground"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                <Mail className="w-4 h-4" />
                Email
              </button>
            </div>

            {/* Contact Input */}
            <div className="space-y-2">
              <Label htmlFor="contact">
                {contactMethod === "phone" ? "Phone Number" : "Email Address"}
              </Label>
              <Input
                id="contact"
                type={contactMethod === "phone" ? "tel" : "email"}
                placeholder={
                  contactMethod === "phone" 
                    ? "+****************" 
                    : "<EMAIL>"
                }
                value={phoneOrEmail}
                onChange={(e) => setPhoneOrEmail(e.target.value)}
                className="rounded-xl h-12"
              />
            </div>

            {/* Role Selector */}
            <div className="space-y-2">
              <Label htmlFor="role">I am a</Label>
              <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as UserRole)}>
                <SelectTrigger className="rounded-xl h-12">
                  <SelectValue placeholder="Select your role" />
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-muted-foreground">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Send OTP Button */}
            <Button
              onClick={handleSendOTP}
              disabled={!phoneOrEmail.trim() || isLoading}
              className="w-full h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
            >
              {isLoading ? "Sending..." : "Send OTP"}
            </Button>

            {/* Register Link */}
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                New here?{" "}
                <button
                  onClick={onRegister}
                  className="text-primary hover:underline font-medium"
                >
                  Create an account
                </button>
              </p>
            </div>
          </div>
        </Card>

        {/* Demo Users Info */}
        <Card className="p-4 rounded-2xl border bg-muted/30">
          <p className="text-xs text-muted-foreground text-center">
            Demo: Use any phone/email with any role to continue
          </p>
        </Card>
      </div>
    </div>
  );
}