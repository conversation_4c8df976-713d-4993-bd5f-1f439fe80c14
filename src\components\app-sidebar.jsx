"use client";

import * as React from "react";
import {
  AudioWaveform,
  BadgeIndianRupee,
  BookOpen,
  Bot,
  ChartColumn,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  ReceiptText,
  Settings2,
  SquareTerminal,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { SidebarLogo } from "@/components/sidebar-logo";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";

// This is sample data.
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/super-admin/dashboard",
      icon: ChartColumn,
      hideDropdown: true,
    },
    {
      title: "Management",
      url: "#",
      icon: SquareTerminal,
      items: [
        {
          title: "Plans",
          url: "/super-admin/management/plans",
        },
        {
          title: "Categories",
          url: "/super-admin/management/categories",
        },
        {
          title: "Sub-Categories",
          url: "/super-admin/management/sub-categories",
        },
        {
          title: "Users",
          url: "/super-admin/management/users",
        },
        {
          title: "Audit Logs",
          url: "/super-admin/management/audit-logs",
        },
        {
          title: "Enquiries",
          url: "/super-admin/management/enquiries",
        },
      ],
    },
    {
      title: "Sales",
      url: "#",
      icon: BadgeIndianRupee,
      items: [
        {
          title: "Commissions Distribution",
          url: "/super-admin/sales/commissions",
        },
        {
          title: "Field Visits",
          url: "/super-admin/sales/field-visits",
        },
        {
          title: "Payments History",
          url: "/super-admin/sales/payments-history",
        },
      ],
    },
    {
      title: "Reports",
      url: "#",
      icon: ReceiptText,
      items: [
        {
          title: "Vendors Onboarding",
          url: "/super-admin/reports/vendors-onboarding",
        },
        {
          title: "Customer Registrations",
          url: "/super-admin/reports/customer-registrations",
        },
        {
          title: "Enquiries",
          url: "/super-admin/reports/enquiries",
        },
        {
          title: "Engagement",
          url: "/super-admin/reports/engagement",
        },
        {
          title: "Plans & Payments",
          url: "/super-admin/reports/plans-payments",
        },
      ],
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "API Keys",
          url: "/super-admin/settings/api-keys",
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarLogo />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
