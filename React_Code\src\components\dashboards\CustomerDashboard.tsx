import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Card } from "../ui/card";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Checkbox } from "../ui/checkbox";
import { Slider } from "../ui/slider";
import { ImageWithFallback } from "../figma/ImageWithFallback";
import { 
  Search, 
  MapPin, 
  Filter, 
  Star, 
  Heart, 
  MessageSquare, 
  Home,
  User,
  Bell,
  Settings,
  SlidersHorizontal,
  ChevronDown,
  TrendingUp,
  Eye
} from "lucide-react";
import { User as UserType } from "../../App";
import { MobileBottomNav } from "../layout/MobileBottomNav";
import { DesktopSidebar } from "../layout/DesktopSidebar";
import { VendorDetailModal } from "../customer/VendorDetailModal";
import { ChartCard, EnquiryChart, CategoryChart } from "../charts/DashboardCharts";

interface CustomerDashboardProps {
  user: UserType;
  onLogout: () => void;
}

export function CustomerDashboard({ user, onLogout }: CustomerDashboardProps) {
  const [currentView, setCurrentView] = useState("home");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("New York, NY");
  const [selectedVendor, setSelectedVendor] = useState<any>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: "all",
    rating: [0],
    priceRange: [0, 1000],
    location: "all",
    verified: false,
    availability: "all"
  });

  // Mock data
  const popularCategories = [
    { name: "Restaurants", icon: "🍽️", count: 1250 },
    { name: "Healthcare", icon: "🏥", count: 890 },
    { name: "Services", icon: "🔧", count: 2340 },
    { name: "Retail", icon: "🛍️", count: 1890 },
    { name: "Technology", icon: "💻", count: 560 },
    { name: "Construction", icon: "🏗️", count: 780 },
  ];

  const trendingVendors = [
    {
      id: 1,
      name: "Giuseppe's Italian Kitchen",
      category: "Restaurants",
      rating: 4.8,
      reviews: 234,
      location: "Downtown, NY",
      image: "🍝",
      isVerified: true,
    },
    {
      id: 2,
      name: "TechFix Solutions",
      category: "Technology",
      rating: 4.9,
      reviews: 156,
      location: "Midtown, NY", 
      image: "💻",
      isVerified: true,
    },
    {
      id: 3,
      name: "Elite Fitness Center",
      category: "Health & Fitness",
      rating: 4.7,
      reviews: 189,
      location: "Brooklyn, NY",
      image: "💪",
      isVerified: true,
    },
  ];

  const recentEnquiries = [
    {
      id: 1,
      vendor: "Giuseppe's Italian Kitchen",
      message: "Catering for 50 people event",
      status: "responded",
      date: "2 days ago",
    },
    {
      id: 2,
      vendor: "TechFix Solutions", 
      message: "MacBook repair service",
      status: "pending",
      date: "1 week ago",
    },
  ];

  const renderHomeView = () => (
    <div className="space-y-6">
      {/* Hero Search Section */}
      <div className="bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Find Your Perfect Vendor</h1>
        <p className="text-white/80 mb-6">
          Discover trusted businesses and services in your area
        </p>
        
        <div className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Search for vendors, services..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12 rounded-xl bg-white text-black"
              />
            </div>
            <Button variant="secondary" size="sm" className="h-12 px-4 rounded-xl bg-white text-primary hover:bg-white/90">
              <Filter className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="flex items-center gap-2 text-sm">
            <MapPin className="w-4 h-4" />
            <span>{selectedLocation}</span>
            <Button variant="ghost" size="sm" className="h-auto p-1 text-white hover:bg-white/20">
              Change
            </Button>
          </div>
        </div>
      </div>

      {/* Popular Categories */}
      <div className="space-y-4">
        <h2 className="text-xl font-bold">Popular Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {popularCategories.map((category) => (
            <Card 
              key={category.name}
              className="p-4 hover:shadow-md transition-shadow cursor-pointer rounded-2xl"
            >
              <div className="text-center space-y-2">
                <div className="text-2xl">{category.icon}</div>
                <div className="font-medium text-sm">{category.name}</div>
                <div className="text-xs text-muted-foreground">
                  {category.count} vendors
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Analytics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard title="Your Enquiry Activity" subtitle="Track your vendor interactions">
          <EnquiryChart />
        </ChartCard>
        
        <ChartCard title="Popular Categories" subtitle="Top vendor categories in your area">
          <CategoryChart />
        </ChartCard>
      </div>

      {/* Trending Vendors */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold">Trending Near You</h2>
          <Button variant="ghost" size="sm" className="text-primary">
            View All
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {trendingVendors.map((vendor) => (
            <Card key={vendor.id} className="p-4 hover:shadow-lg transition-shadow cursor-pointer rounded-2xl">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="text-3xl">{vendor.image}</div>
                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="font-semibold flex items-center gap-2">
                          {vendor.name}
                          {vendor.isVerified && (
                            <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                              ✓ Verified
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {vendor.category}
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" className="p-1">
                        <Heart className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{vendor.rating}</span>
                    <span className="text-sm text-muted-foreground">
                      ({vendor.reviews})
                    </span>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <MapPin className="w-4 h-4" />
                    {vendor.location}
                  </div>
                </div>
                
                <Button 
                  onClick={() => setSelectedVendor(vendor)}
                  className="w-full h-9 rounded-xl bg-primary hover:bg-primary/90 text-white"
                >
                  View Details
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSearchView = () => {
    const mockSearchResults = [
      {
        id: 1,
        name: "Digital Marketing Pro",
        category: "Technology",
        rating: 4.9,
        reviews: 187,
        location: "Manhattan, NY",
        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400",
        isVerified: true,
        price: "$50-100/hr",
        responseTime: "2 hours",
        tags: ["SEO", "Social Media", "Analytics"]
      },
      {
        id: 2,
        name: "Elite Catering Services",
        category: "Restaurants & Food",
        rating: 4.8,
        reviews: 234,
        location: "Brooklyn, NY",
        image: "https://images.unsplash.com/photo-1555244162-803834f70033?w=400",
        isVerified: true,
        price: "$25-50/person",
        responseTime: "1 hour",
        tags: ["Corporate Events", "Weddings", "Parties"]
      },
      {
        id: 3,
        name: "Modern Architecture Studio",
        category: "Construction",
        rating: 4.7,
        reviews: 156,
        location: "Queens, NY",
        image: "https://images.unsplash.com/photo-1503387762-592deb58ef4e?w=400",
        isVerified: true,
        price: "$100-200/hr",
        responseTime: "4 hours",
        tags: ["Residential", "Commercial", "Interior"]
      }
    ];

    const filteredResults = mockSearchResults.filter(vendor => {
      if (searchQuery && !vendor.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
          !vendor.category.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      if (filters.category !== "all" && vendor.category !== filters.category) return false;
      if (filters.rating[0] > 0 && vendor.rating < filters.rating[0]) return false;
      if (filters.verified && !vendor.isVerified) return false;
      return true;
    });

    return (
      <div className="space-y-6">
        {/* Search Header */}
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <Input
                placeholder="Search vendors, services, categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12 rounded-xl"
              />
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowFilters(!showFilters)}
              className="h-12 rounded-xl"
            >
              <SlidersHorizontal className="w-4 h-4 mr-2" />
              Filters
              <ChevronDown className={`w-4 h-4 ml-2 transition-transform ${showFilters ? "rotate-180" : ""}`} />
            </Button>
          </div>

          {/* Quick Filters */}
          <div className="flex flex-wrap gap-2">
            {["All", "Technology", "Restaurants & Food", "Healthcare", "Services", "Construction"].map((category) => (
              <Button
                key={category}
                variant={filters.category === category.toLowerCase() || (category === "All" && filters.category === "all") ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters(prev => ({ ...prev, category: category === "All" ? "all" : category }))}
                className="rounded-xl"
              >
                {category}
              </Button>
            ))}
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <Card className="p-6 rounded-2xl">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Location</label>
                  <Select value={filters.location} onValueChange={(value) => setFilters(prev => ({ ...prev, location: value }))}>
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Locations</SelectItem>
                      <SelectItem value="manhattan">Manhattan</SelectItem>
                      <SelectItem value="brooklyn">Brooklyn</SelectItem>
                      <SelectItem value="queens">Queens</SelectItem>
                      <SelectItem value="bronx">Bronx</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Minimum Rating</label>
                  <div className="px-3">
                    <Slider
                      value={filters.rating}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, rating: value }))}
                      max={5}
                      min={0}
                      step={0.5}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>0</span>
                      <span>{filters.rating[0]} stars</span>
                      <span>5</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Availability</label>
                  <Select value={filters.availability} onValueChange={(value) => setFilters(prev => ({ ...prev, availability: value }))}>
                    <SelectTrigger className="rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Any Time</SelectItem>
                      <SelectItem value="immediate">Available Now</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Preferences</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="verified"
                        checked={filters.verified}
                        onCheckedChange={(checked) => setFilters(prev => ({ ...prev, verified: checked as boolean }))}
                      />
                      <label htmlFor="verified" className="text-sm">Verified only</label>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Results Header */}
        {searchQuery && (
          <div className="flex items-center justify-between">
            <p className="text-muted-foreground">
              Found {filteredResults.length} results for "{searchQuery}"
            </p>
            <Select defaultValue="relevance">
              <SelectTrigger className="w-40 rounded-xl">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">Most Relevant</SelectItem>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="reviews">Most Reviews</SelectItem>
                <SelectItem value="newest">Newest First</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Search Results */}
        {filteredResults.length > 0 ? (
          <div className="space-y-4">
            {filteredResults.map((vendor) => (
              <Card key={vendor.id} className="p-6 rounded-2xl hover:shadow-lg transition-shadow cursor-pointer">
                <div className="flex items-start gap-4">
                  <div className="w-20 h-20 rounded-2xl overflow-hidden bg-muted">
                    <ImageWithFallback
                      src={vendor.image}
                      alt={vendor.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="flex-1 space-y-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-lg">{vendor.name}</h3>
                          {vendor.isVerified && (
                            <Badge className="bg-green-100 text-green-800 text-xs">
                              ✓ Verified
                            </Badge>
                          )}
                        </div>
                        <p className="text-muted-foreground">{vendor.category}</p>
                        <div className="flex items-center gap-4 mt-2">
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-medium">{vendor.rating}</span>
                            <span className="text-sm text-muted-foreground">({vendor.reviews})</span>
                          </div>
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <MapPin className="w-4 h-4" />
                            {vendor.location}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="font-semibold text-primary">{vendor.price}</div>
                        <div className="text-sm text-muted-foreground">Response: {vendor.responseTime}</div>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      {vendor.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm" className="p-2 rounded-xl">
                          <Heart className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="rounded-xl">
                          <MessageSquare className="w-4 h-4 mr-2" />
                          Quick Enquiry
                        </Button>
                      </div>
                      <Button 
                        onClick={() => setSelectedVendor(vendor)}
                        className="rounded-xl bg-primary hover:bg-primary/90"
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : searchQuery ? (
          <div className="text-center py-20 text-muted-foreground">
            <Search className="mx-auto w-12 h-12 mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">No results found</h3>
            <p>Try adjusting your search terms or filters</p>
          </div>
        ) : (
          <div className="text-center py-20 text-muted-foreground">
            <Search className="mx-auto w-12 h-12 mb-4" />
            <h3 className="text-lg font-medium mb-2">Search for Vendors</h3>
            <p>Find the perfect vendor for your needs</p>
          </div>
        )}
      </div>
    );
  };

  const renderFavoritesView = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">My Favorites</h1>
      <div className="text-center py-20 text-muted-foreground">
        <Heart className="mx-auto w-12 h-12 mb-4" />
        <h3 className="text-lg font-medium mb-2">No Favorites Yet</h3>
        <p>Start exploring and save vendors you like</p>
      </div>
    </div>
  );

  const renderEnquiriesView = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">My Enquiries</h1>
      
      <div className="space-y-4">
        {recentEnquiries.map((enquiry) => (
          <Card key={enquiry.id} className="p-4 rounded-2xl">
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <div>
                  <div className="font-semibold">{enquiry.vendor}</div>
                  <div className="text-sm text-muted-foreground">
                    {enquiry.message}
                  </div>
                </div>
                <Badge 
                  variant={enquiry.status === "responded" ? "default" : "secondary"}
                  className={enquiry.status === "responded" ? "bg-green-100 text-green-800" : ""}
                >
                  {enquiry.status === "responded" ? "Responded" : "Pending"}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{enquiry.date}</span>
                <Button variant="outline" size="sm" className="rounded-lg">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  View Details
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderProfileView = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">My Profile</h1>
      
      <Card className="p-6 rounded-2xl">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Avatar className="w-16 h-16">
              <AvatarFallback className="text-lg font-semibold bg-primary text-white">
                {user.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-semibold">{user.name}</h2>
              <p className="text-muted-foreground">{user.email}</p>
              <p className="text-muted-foreground">{user.phone}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="h-12 rounded-xl">
              <Settings className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
            <Button 
              variant="outline" 
              onClick={onLogout}
              className="h-12 rounded-xl text-destructive hover:text-destructive"
            >
              Logout
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case "home": return renderHomeView();
      case "search": return renderSearchView();
      case "favorites": return renderFavoritesView();
      case "enquiries": return renderEnquiriesView();
      case "profile": return renderProfileView();
      default: return renderHomeView();
    }
  };

  const navigationItems = [
    { id: "home", label: "Home", icon: Home },
    { id: "search", label: "Search", icon: Search },
    { id: "favorites", label: "Favorites", icon: Heart },
    { id: "enquiries", label: "Enquiries", icon: MessageSquare },
    { id: "profile", label: "Profile", icon: User },
  ];

  const handleSendEnquiry = (vendor: any, message: string) => {
    console.log("Sending enquiry to:", vendor.name, "Message:", message);
    // Handle enquiry logic here
    setSelectedVendor(null);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Desktop Layout */}
      <div className="hidden md:flex h-screen">
        <DesktopSidebar
          user={user}
          navigationItems={navigationItems}
          currentView={currentView}
          onNavigate={setCurrentView}
          onLogout={onLogout}
        />
        <main className="flex-1 overflow-y-auto">
          <div className="p-6 max-w-7xl mx-auto">
            {renderCurrentView()}
          </div>
        </main>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden">
        <main className="pb-20">
          <div className="p-4">
            {currentView !== "home" && (
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-xl font-bold">
                  {navigationItems.find(item => item.id === currentView)?.label}
                </h1>
                <Button variant="ghost" size="sm" className="p-2">
                  <Bell className="w-5 h-5" />
                </Button>
              </div>
            )}
            {renderCurrentView()}
          </div>
        </main>
        
        <MobileBottomNav
          navigationItems={navigationItems}
          currentView={currentView}
          onNavigate={setCurrentView}
        />
      </div>

      {/* Vendor Detail Modal */}
      {selectedVendor && (
        <VendorDetailModal
          vendor={selectedVendor}
          onClose={() => setSelectedVendor(null)}
          onSendEnquiry={handleSendEnquiry}
        />
      )}
    </div>
  );
}