import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import { Card } from "../ui/card";

const COLORS = ["#1D4ED8", "#F97316", "#10b981", "#f59e0b", "#8b5cf6"];

// Sample data generators
export const generateRevenueData = () => [
  { month: "Jan", revenue: 45000, target: 50000 },
  { month: "Feb", revenue: 52000, target: 55000 },
  { month: "Mar", revenue: 48000, target: 52000 },
  { month: "Apr", revenue: 61000, target: 58000 },
  { month: "May", revenue: 55000, target: 60000 },
  { month: "Jun", revenue: 67000, target: 65000 },
];

export const generateUserGrowthData = () => [
  { month: "Jan", customers: 1200, vendors: 340, salesExecs: 25 },
  { month: "Feb", customers: 1480, vendors: 380, salesExecs: 28 },
  { month: "Mar", customers: 1720, vendors: 420, salesExecs: 32 },
  { month: "Apr", customers: 2100, vendors: 480, salesExecs: 35 },
  { month: "May", customers: 2580, vendors: 520, salesExecs: 38 },
  { month: "Jun", customers: 2890, vendors: 580, salesExecs: 42 },
];

export const generateEnquiryData = () => [
  { day: "Mon", enquiries: 45, responses: 38 },
  { day: "Tue", enquiries: 52, responses: 45 },
  { day: "Wed", enquiries: 38, responses: 35 },
  { day: "Thu", enquiries: 64, responses: 58 },
  { day: "Fri", enquiries: 58, responses: 52 },
  { day: "Sat", enquiries: 42, responses: 38 },
  { day: "Sun", enquiries: 35, responses: 32 },
];

export const generateCategoryData = () => [
  { name: "Restaurants", value: 340, percentage: 28 },
  { name: "Technology", value: 180, percentage: 15 },
  { name: "Healthcare", value: 210, percentage: 17 },
  { name: "Services", value: 280, percentage: 23 },
  { name: "Retail", value: 200, percentage: 17 },
];

export function ChartCard({ title, subtitle, children, className = "" }) {
  return (
    <Card className={`p-6 rounded-2xl ${className}`}>
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">{title}</h3>
          {subtitle && (
            <p className="text-sm text-muted-foreground">{subtitle}</p>
          )}
        </div>
        <div className="h-80">{children}</div>
      </div>
    </Card>
  );
}

export function RevenueChart() {
  const data = generateRevenueData();

  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
        <XAxis
          dataKey="month"
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
        />
        <YAxis
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
          tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
        />
        <Tooltip
          formatter={(value) => [`$${value.toLocaleString()}`, "Revenue"]}
          labelStyle={{ color: "#1f2937" }}
          contentStyle={{
            backgroundColor: "white",
            border: "none",
            borderRadius: "12px",
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
          }}
        />
        <Area
          type="monotone"
          dataKey="revenue"
          stroke="#1D4ED8"
          fill="url(#colorRevenue)"
          strokeWidth={3}
        />
        <Line
          type="monotone"
          dataKey="target"
          stroke="#F97316"
          strokeDasharray="5 5"
          strokeWidth={2}
        />
        <defs>
          <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#1D4ED8" stopOpacity={0.3} />
            <stop offset="95%" stopColor="#1D4ED8" stopOpacity={0} />
          </linearGradient>
        </defs>
      </AreaChart>
    </ResponsiveContainer>
  );
}

export function UserGrowthChart() {
  const data = generateUserGrowthData();

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
        <XAxis
          dataKey="month"
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
        />
        <YAxis
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "white",
            border: "none",
            borderRadius: "12px",
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
          }}
        />
        <Line
          type="monotone"
          dataKey="customers"
          stroke="#1D4ED8"
          strokeWidth={3}
          dot={{ fill: "#1D4ED8", strokeWidth: 2, r: 6 }}
        />
        <Line
          type="monotone"
          dataKey="vendors"
          stroke="#F97316"
          strokeWidth={3}
          dot={{ fill: "#F97316", strokeWidth: 2, r: 6 }}
        />
        <Line
          type="monotone"
          dataKey="salesExecs"
          stroke="#10b981"
          strokeWidth={3}
          dot={{ fill: "#10b981", strokeWidth: 2, r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}

export function EnquiryChart() {
  const data = generateEnquiryData();

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data} barCategoryGap={20}>
        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
        <XAxis
          dataKey="day"
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
        />
        <YAxis
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "white",
            border: "none",
            borderRadius: "12px",
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
          }}
        />
        <Bar
          dataKey="enquiries"
          fill="#1D4ED8"
          radius={[4, 4, 0, 0]}
          name="Enquiries"
        />
        <Bar
          dataKey="responses"
          fill="#F97316"
          radius={[4, 4, 0, 0]}
          name="Responses"
        />
      </BarChart>
    </ResponsiveContainer>
  );
}

export function CategoryChart() {
  const data = generateCategoryData();

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={120}
          paddingAngle={5}
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip
          formatter={(value, name) => [value, "Vendors"]}
          contentStyle={{
            backgroundColor: "white",
            border: "none",
            borderRadius: "12px",
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
          }}
        />
      </PieChart>
    </ResponsiveContainer>
  );
}

export function CommissionChart() {
  const data = [
    { month: "Jan", earned: 1250, paid: 1250 },
    { month: "Feb", earned: 1480, paid: 1250 },
    { month: "Mar", earned: 1720, paid: 1480 },
    { month: "Apr", earned: 2100, paid: 1720 },
    { month: "May", earned: 1950, paid: 2100 },
    { month: "Jun", earned: 2340, paid: 1950 },
  ];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
        <XAxis
          dataKey="month"
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
        />
        <YAxis
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
          tickFormatter={(value) => `$${(value / 1000).toFixed(1)}K`}
        />
        <Tooltip
          formatter={(value) => [`$${value.toLocaleString()}`, ""]}
          contentStyle={{
            backgroundColor: "white",
            border: "none",
            borderRadius: "12px",
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
          }}
        />
        <Area
          type="monotone"
          dataKey="earned"
          stackId="1"
          stroke="#10b981"
          fill="#10b981"
          fillOpacity={0.8}
          name="Earned"
        />
        <Area
          type="monotone"
          dataKey="paid"
          stackId="2"
          stroke="#1D4ED8"
          fill="#1D4ED8"
          fillOpacity={0.8}
          name="Paid"
        />
      </AreaChart>
    </ResponsiveContainer>
  );
}

export function VendorPerformanceChart() {
  const data = [
    { name: "Views", value: 1240, target: 1500 },
    { name: "Enquiries", value: 89, target: 100 },
    { name: "Conversions", value: 23, target: 30 },
    { name: "Reviews", value: 18, target: 20 },
  ];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data} layout="horizontal">
        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
        <XAxis
          type="number"
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
        />
        <YAxis
          type="category"
          dataKey="name"
          axisLine={false}
          tickLine={false}
          tick={{ fontSize: 12, fill: "#6b7280" }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "white",
            border: "none",
            borderRadius: "12px",
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
          }}
        />
        <Bar
          dataKey="value"
          fill="#1D4ED8"
          radius={[0, 4, 4, 0]}
          name="Current"
        />
        <Bar
          dataKey="target"
          fill="#e5e7eb"
          radius={[0, 4, 4, 0]}
          name="Target"
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
