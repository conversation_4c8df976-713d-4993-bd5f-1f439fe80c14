import { Button } from "../ui/button";
import { Building2, Users, Star, TrendingUp } from "lucide-react";

interface WelcomeScreenProps {
  onGetStarted: () => void;
}

export function WelcomeScreen({ onGetStarted }: WelcomeScreenProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8 text-center">
        {/* Logo */}
        <div className="space-y-4">
          <div className="mx-auto w-20 h-20 bg-primary rounded-2xl flex items-center justify-center shadow-lg">
            <Building2 className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-foreground">VendorHub</h1>
          <p className="text-muted-foreground text-lg">
            Connect with trusted vendors and grow your business
          </p>
        </div>

        {/* Features */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="flex items-center gap-3 p-4 rounded-2xl bg-card border shadow-sm">
              <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                <Users className="w-5 h-5 text-primary" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold">Multi-Role Platform</h3>
                <p className="text-sm text-muted-foreground">
                  For vendors, customers, and sales teams
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-4 rounded-2xl bg-card border shadow-sm">
              <div className="w-10 h-10 bg-accent/10 rounded-xl flex items-center justify-center">
                <Star className="w-5 h-5 text-accent" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold">Trusted Reviews</h3>
                <p className="text-sm text-muted-foreground">
                  Real feedback from verified customers
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-4 rounded-2xl bg-card border shadow-sm">
              <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-green-600" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold">Business Growth</h3>
                <p className="text-sm text-muted-foreground">
                  Advanced analytics and insights
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Button */}
        <div className="space-y-4">
          <Button 
            onClick={onGetStarted}
            size="lg"
            className="w-full h-14 rounded-2xl bg-primary hover:bg-primary/90 text-white shadow-lg"
          >
            Get Started
          </Button>
          
          <p className="text-sm text-muted-foreground">
            Join thousands of businesses already growing with VendorHub
          </p>
        </div>
      </div>
    </div>
  );
}