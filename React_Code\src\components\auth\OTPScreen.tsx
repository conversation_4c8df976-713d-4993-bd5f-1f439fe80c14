import { useState, useEffect } from "react";
import { Button } from "../ui/button";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "../ui/input-otp";
import { Card } from "../ui/card";
import { Building2, ArrowLeft, CheckCircle } from "lucide-react";

interface OTPScreenProps {
  phone: string;
  onVerification: (success: boolean) => void;
  onBack: () => void;
}

export function OTPScreen({ phone, onVerification, onBack }: OTPScreenProps) {
  const [otp, setOtp] = useState("");
  const [countdown, setCountdown] = useState(60);
  const [isResending, setIsResending] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleVerify = async () => {
    if (otp.length !== 6) return;
    
    setIsVerifying(true);
    
    // Simulate verification delay
    setTimeout(() => {
      setIsVerifying(false);
      setIsVerified(true);
      
      // Show success state briefly before redirecting
      setTimeout(() => {
        onVerification(true);
      }, 1000);
    }, 1500);
  };

  const handleResend = () => {
    setIsResending(true);
    setCountdown(60);
    setOtp("");
    
    setTimeout(() => {
      setIsResending(false);
    }, 1000);
  };

  const formatPhone = (phone: string) => {
    if (phone.includes("@")) return phone; // Email
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  };

  if (isVerified) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 flex items-center justify-center p-4">
        <Card className="w-full max-w-md p-8 rounded-2xl border shadow-lg text-center">
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-foreground">Verified!</h2>
            <p className="text-muted-foreground">
              Your account has been successfully verified
            </p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-2 rounded-xl"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div className="flex-1 text-center">
            <div className="mx-auto w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
              <Building2 className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        {/* OTP Form */}
        <Card className="p-6 rounded-2xl border shadow-lg">
          <div className="space-y-6 text-center">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-foreground">Enter Verification Code</h1>
              <p className="text-muted-foreground">
                We've sent a 6-digit code to<br />
                <span className="font-medium text-foreground">{formatPhone(phone)}</span>
              </p>
            </div>

            {/* OTP Input */}
            <div className="space-y-4">
              <div className="flex justify-center">
                <InputOTP
                  maxLength={6}
                  value={otp}
                  onChange={setOtp}
                  onComplete={handleVerify}
                >
                  <InputOTPGroup>
                    <InputOTPSlot index={0} className="w-12 h-12 rounded-xl border-2" />
                    <InputOTPSlot index={1} className="w-12 h-12 rounded-xl border-2" />
                    <InputOTPSlot index={2} className="w-12 h-12 rounded-xl border-2" />
                    <InputOTPSlot index={3} className="w-12 h-12 rounded-xl border-2" />
                    <InputOTPSlot index={4} className="w-12 h-12 rounded-xl border-2" />
                    <InputOTPSlot index={5} className="w-12 h-12 rounded-xl border-2" />
                  </InputOTPGroup>
                </InputOTP>
              </div>
            </div>

            {/* Verify Button */}
            <Button
              onClick={handleVerify}
              disabled={otp.length !== 6 || isVerifying}
              className="w-full h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
            >
              {isVerifying ? "Verifying..." : "Verify Code"}
            </Button>

            {/* Resend Code */}
            <div className="space-y-2">
              {countdown > 0 ? (
                <p className="text-sm text-muted-foreground">
                  Resend code in <span className="font-medium text-foreground">{countdown}s</span>
                </p>
              ) : (
                <Button
                  variant="ghost"
                  onClick={handleResend}
                  disabled={isResending}
                  className="h-auto p-0 text-primary hover:bg-transparent"
                >
                  {isResending ? "Sending..." : "Resend Code"}
                </Button>
              )}
            </div>
          </div>
        </Card>

        {/* Demo Info */}
        <Card className="p-4 rounded-2xl border bg-muted/30">
          <p className="text-xs text-muted-foreground text-center">
            Demo: Enter any 6-digit code to continue
          </p>
        </Card>
      </div>
    </div>
  );
}