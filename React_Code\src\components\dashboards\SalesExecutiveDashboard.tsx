import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Badge } from "../ui/badge";
import { Input } from "../ui/input";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { 
  BarChart3, 
  MapPin, 
  Building2, 
  DollarSign,
  Plus,
  Search,
  UserPlus,
  Calendar,
  TrendingUp,
  Clock,
  CheckCircle
} from "lucide-react";
import { User as UserType } from "../../App";
import { MobileBottomNav } from "../layout/MobileBottomNav";
import { DesktopSidebar } from "../layout/DesktopSidebar";

interface SalesExecutiveDashboardProps {
  user: UserType;
  onLogout: () => void;
}

export function SalesExecutiveDashboard({ user, onLogout }: SalesExecutiveDashboardProps) {
  const [currentView, setCurrentView] = useState("dashboard");

  // Mock data
  const salesStats = {
    vendorsOnboarded: 28,
    feesCollected: 2790,
    commissionEarned: 1395,
    thisMonthTarget: 35,
    commissionRate: 0.5, // 50% commission
  };

  const recentVendors = [
    {
      id: 1,
      businessName: "Giuseppe's Italian Kitchen",
      contactPerson: "Mario Giuseppe",
      category: "Restaurants & Food",
      location: "Brooklyn, NY",
      onboardedDate: "2024-01-15",
      status: "approved",
      fee: 99,
    },
    {
      id: 2,
      businessName: "TechFix Solutions",
      contactPerson: "John Smith",
      category: "Technology",
      location: "Manhattan, NY",
      onboardedDate: "2024-01-14",
      status: "pending",
      fee: 99,
    },
    {
      id: 3,
      businessName: "Green Garden Landscaping",
      contactPerson: "Lisa Johnson",
      category: "Services",
      location: "Queens, NY",
      onboardedDate: "2024-01-12",
      status: "approved",
      fee: 99,
    },
  ];

  const commissionData = [
    {
      month: "December 2024",
      vendorsOnboarded: 15,
      feesCollected: 1485,
      commissionEarned: 742.5,
      status: "paid",
    },
    {
      month: "January 2025",
      vendorsOnboarded: 28,
      feesCollected: 2790,
      commissionEarned: 1395,
      status: "pending",
    },
  ];

  const visitLogs = [
    {
      id: 1,
      location: "Mario's Restaurant, Brooklyn",
      purpose: "Initial business meeting",
      date: "2024-01-15",
      time: "10:30 AM",
      outcome: "Onboarded successfully",
      status: "completed"
    },
    {
      id: 2,
      location: "Tech Hub, Manhattan", 
      purpose: "Follow-up on document verification",
      date: "2024-01-14",
      time: "2:15 PM",
      outcome: "Documents submitted",
      status: "completed"
    },
  ];

  const renderDashboardView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Sales Dashboard</h1>
          <p className="text-muted-foreground">Track your vendor onboarding performance and earnings</p>
        </div>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          <UserPlus className="w-4 h-4 mr-2" />
          Onboard Vendor
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Building2 className="w-5 h-5 text-primary" />
              <span className="text-sm text-green-600 font-medium">
                {Math.round((salesStats.vendorsOnboarded / salesStats.thisMonthTarget) * 100)}%
              </span>
            </div>
            <div className="text-2xl font-bold">{salesStats.vendorsOnboarded}</div>
            <div className="text-sm text-muted-foreground">
              Vendors Onboarded
              <div className="text-xs text-muted-foreground">Target: {salesStats.thisMonthTarget}</div>
            </div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <DollarSign className="w-5 h-5 text-green-500" />
              <span className="text-sm text-green-600 font-medium">+18%</span>
            </div>
            <div className="text-2xl font-bold">${salesStats.feesCollected}</div>
            <div className="text-sm text-muted-foreground">Fees Collected</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <TrendingUp className="w-5 h-5 text-accent" />
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
                Pending
              </Badge>
            </div>
            <div className="text-2xl font-bold">${salesStats.commissionEarned}</div>
            <div className="text-sm text-muted-foreground">Commission Earned</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <MapPin className="w-5 h-5 text-blue-500" />
              <span className="text-sm text-muted-foreground">Today</span>
            </div>
            <div className="text-2xl font-bold">3</div>
            <div className="text-sm text-muted-foreground">Scheduled Visits</div>
          </div>
        </Card>
      </div>

      {/* Progress Chart */}
      <Card className="p-6 rounded-2xl">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Monthly Progress</h3>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Target Progress</span>
              <span>{salesStats.vendorsOnboarded}/{salesStats.thisMonthTarget} vendors</span>
            </div>
            <div className="w-full bg-muted rounded-full h-3">
              <div 
                className="bg-primary h-3 rounded-full transition-all duration-300"
                style={{ width: `${Math.min((salesStats.vendorsOnboarded / salesStats.thisMonthTarget) * 100, 100)}%` }}
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 rounded-2xl">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Onboardings</h3>
              <Button variant="ghost" size="sm" onClick={() => setCurrentView("vendors")}>
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {recentVendors.slice(0, 3).map((vendor) => (
                <div key={vendor.id} className="flex items-start gap-3 p-3 bg-muted/30 rounded-xl">
                  <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                    <Building2 className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-1">
                      <div>
                        <h4 className="font-medium text-sm">{vendor.businessName}</h4>
                        <p className="text-xs text-muted-foreground">{vendor.category}</p>
                        <p className="text-xs text-muted-foreground">{vendor.location}</p>
                      </div>
                      <div className="text-right">
                        <Badge 
                          variant={vendor.status === "approved" ? "default" : "secondary"}
                          className={`text-xs ${
                            vendor.status === "approved" 
                              ? "bg-green-100 text-green-800" 
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {vendor.status}
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">
                          ${vendor.fee}
                        </div>
                      </div>
                    </div>
                    <span className="text-xs text-muted-foreground">{vendor.onboardedDate}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Field Visit Logs</h3>
              <Button variant="ghost" size="sm" onClick={() => setCurrentView("visits")}>
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {visitLogs.slice(0, 2).map((visit) => (
                <div key={visit.id} className="p-3 bg-muted/30 rounded-xl">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium text-sm">{visit.location}</h4>
                        <p className="text-xs text-muted-foreground">{visit.purpose}</p>
                      </div>
                      <div className="text-xs text-muted-foreground text-right">
                        <div>{visit.date}</div>
                        <div>{visit.time}</div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-foreground">{visit.outcome}</span>
                      <Badge className="bg-green-100 text-green-800 text-xs">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        {visit.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderVendorsView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">My Vendors</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="rounded-xl">
            Export List
          </Button>
          <Button className="rounded-xl bg-primary hover:bg-primary/90">
            <UserPlus className="w-4 h-4 mr-2" />
            Onboard New Vendor
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input placeholder="Search vendors..." className="pl-10 rounded-xl" />
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-40 rounded-xl">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Vendors List */}
      <div className="space-y-4">
        {recentVendors.map((vendor) => (
          <Card key={vendor.id} className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{vendor.businessName}</h3>
                    <p className="text-muted-foreground">Contact: {vendor.contactPerson}</p>
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      <span>{vendor.category}</span>
                      <span>•</span>
                      <span>{vendor.location}</span>
                      <span>•</span>
                      <span>Onboarded: {vendor.onboardedDate}</span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Badge 
                    variant={vendor.status === "approved" ? "default" : "secondary"}
                    className={
                      vendor.status === "approved" 
                        ? "bg-green-100 text-green-800" 
                        : "bg-yellow-100 text-yellow-800"
                    }
                  >
                    {vendor.status}
                  </Badge>
                  <div className="text-sm font-semibold text-accent">
                    Fee: ${vendor.fee}
                  </div>
                </div>
              </div>
              
              <div className="flex gap-3">
                <Button size="sm" className="rounded-lg">
                  View Details
                </Button>
                <Button variant="outline" size="sm" className="rounded-lg">
                  <MapPin className="w-4 h-4 mr-2" />
                  Visit Location
                </Button>
                {vendor.status === "pending" && (
                  <Button variant="ghost" size="sm" className="rounded-lg">
                    Follow Up
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderCommissionView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Commission Tracking</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="rounded-xl">
            Download Statement
          </Button>
        </div>
      </div>

      {/* Commission Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-6 rounded-2xl">
          <div className="space-y-2">
            <h3 className="font-semibold text-accent">Total Earned</h3>
            <div className="text-3xl font-bold">
              ${commissionData.reduce((acc, curr) => acc + curr.commissionEarned, 0)}
            </div>
            <p className="text-sm text-muted-foreground">All time commission</p>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <div className="space-y-2">
            <h3 className="font-semibold text-green-600">Paid Out</h3>
            <div className="text-3xl font-bold">
              ${commissionData.filter(c => c.status === "paid").reduce((acc, curr) => acc + curr.commissionEarned, 0)}
            </div>
            <p className="text-sm text-muted-foreground">Total paid commission</p>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <div className="space-y-2">
            <h3 className="font-semibold text-yellow-600">Pending</h3>
            <div className="text-3xl font-bold">
              ${commissionData.filter(c => c.status === "pending").reduce((acc, curr) => acc + curr.commissionEarned, 0)}
            </div>
            <p className="text-sm text-muted-foreground">Awaiting payment</p>
          </div>
        </Card>
      </div>

      {/* Commission History */}
      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Commission History</h3>
        <div className="space-y-4">
          {commissionData.map((period, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-muted/30 rounded-xl">
              <div>
                <h4 className="font-semibold">{period.month}</h4>
                <p className="text-sm text-muted-foreground">
                  {period.vendorsOnboarded} vendors onboarded
                </p>
              </div>
              <div className="text-right">
                <div className="font-semibold text-lg">
                  ${period.commissionEarned}
                </div>
                <div className="text-sm text-muted-foreground">
                  from ${period.feesCollected} fees
                </div>
                <Badge 
                  variant={period.status === "paid" ? "default" : "secondary"}
                  className={`mt-1 ${
                    period.status === "paid" 
                      ? "bg-green-100 text-green-800" 
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {period.status}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Commission Rate Info */}
      <Card className="p-6 rounded-2xl bg-primary/5 border-primary/20">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
            <TrendingUp className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold">Commission Rate</h3>
            <p className="text-sm text-muted-foreground">
              You earn {salesStats.commissionRate * 100}% commission on every successful vendor onboarding
            </p>
          </div>
          <div className="ml-auto text-right">
            <div className="text-2xl font-bold text-primary">
              {salesStats.commissionRate * 100}%
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderVisitsView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Field Visit Logs</h1>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Log New Visit
        </Button>
      </div>

      <div className="space-y-4">
        {visitLogs.map((visit) => (
          <Card key={visit.id} className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{visit.location}</h3>
                    <p className="text-muted-foreground">{visit.purpose}</p>
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {visit.date}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {visit.time}
                      </span>
                    </div>
                  </div>
                </div>
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  {visit.status}
                </Badge>
              </div>
              
              <div className="p-4 bg-muted/30 rounded-xl">
                <h4 className="font-medium text-sm mb-2">Visit Outcome</h4>
                <p className="text-sm text-foreground">{visit.outcome}</p>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Map placeholder */}
      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Visit Locations Map</h3>
        <div className="h-64 bg-muted/30 rounded-xl flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <MapPin className="mx-auto w-12 h-12 mb-4" />
            <h4 className="font-medium mb-2">Location Map</h4>
            <p className="text-sm">Interactive map showing all your field visit locations</p>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case "dashboard": return renderDashboardView();
      case "vendors": return renderVendorsView();
      case "commission": return renderCommissionView();
      case "visits": return renderVisitsView();
      default: return renderDashboardView();
    }
  };

  const navigationItems = [
    { id: "dashboard", label: "Dashboard", icon: BarChart3 },
    { id: "vendors", label: "My Vendors", icon: Building2 },
    { id: "commission", label: "Commission", icon: DollarSign },
    { id: "visits", label: "Field Visits", icon: MapPin },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Desktop Layout */}
      <div className="hidden md:flex h-screen">
        <DesktopSidebar
          user={user}
          navigationItems={navigationItems}
          currentView={currentView}
          onNavigate={setCurrentView}
          onLogout={onLogout}
        />
        <main className="flex-1 overflow-y-auto">
          <div className="p-6 max-w-7xl mx-auto">
            {renderCurrentView()}
          </div>
        </main>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden">
        <main className="pb-20">
          <div className="p-4">
            {renderCurrentView()}
          </div>
        </main>
        
        <MobileBottomNav
          navigationItems={navigationItems}
          currentView={currentView}
          onNavigate={setCurrentView}
        />
      </div>
    </div>
  );
}