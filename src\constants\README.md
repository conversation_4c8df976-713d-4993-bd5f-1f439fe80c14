# Dynamic Navigation System

This directory contains the configuration for the dynamic navigation system that adapts the sidebar based on user roles.

## Files

### `navLinks.js`
Contains the navigation configuration for different user roles including:
- **super-admin**: Full system access with management, sales, reports, and settings
- **admin**: User and content management with basic reporting
- **vendor**: Service management, enquiries, and payments
- **customer**: Basic dashboard, enquiries, and favorites
- **sales-executive**: Field visits, leads, and commission tracking

### `companyName.jsx`
Contains the company name constant used throughout the application.

## Usage

### Basic Usage
```javascript
import { AppSidebar } from "@/components/app-sidebar";

// Use with explicit role
<AppSidebar role="super-admin" />

// Use with automatic role detection
<AppSidebar />
```

### Getting Navigation Data
```javascript
import { getNavigationForRole, getAvailableRoles } from "@/constants/navLinks";

// Get navigation for specific role
const navData = getNavigationForRole("admin");

// Get all available roles
const roles = getAvailableRoles();
```

### Role Management
```javascript
import { 
  getCurrentUserRole, 
  setCurrentUserRole, 
  getRoleDisplayName 
} from "@/utils/roleUtils";

// Get current user role
const role = getCurrentUserRole();

// Set user role (for demo/testing)
setCurrentUserRole("vendor");

// Get display name for role
const displayName = getRoleDisplayName("super-admin"); // "Super Administrator"
```

## Navigation Structure

Each role configuration includes:
- `user`: User information (name, email, avatar)
- `navMain`: Array of navigation items with:
  - `title`: Display name
  - `url`: Navigation URL
  - `icon`: Lucide React icon component
  - `hideDropdown`: Boolean to hide dropdown for single items
  - `items`: Array of sub-navigation items (for dropdowns)

## Adding New Roles

1. Add new role configuration to `navigationConfig` in `navLinks.js`
2. Update role hierarchy in `roleUtils.js` if needed
3. Add role display name to `getRoleDisplayName` function
4. Add redirect URL to `getRoleBasedRedirectUrl` function

## Demo

The super-admin dashboard includes a role switcher component that demonstrates the dynamic navigation system. Users can switch between different roles to see how the sidebar adapts.

## Integration with Authentication

In a production environment, replace the mock `getCurrentUserRole` function with actual authentication logic that retrieves the user's role from:
- JWT tokens
- Session storage
- API calls
- Authentication context/state management
