import { useState, useEffect } from "react";
import { WelcomeScreen } from "./components/auth/WelcomeScreen";
import { LoginScreen } from "./components/auth/LoginScreen";
import { RegisterScreen } from "./components/auth/RegisterScreen";
import { OTPScreen } from "./components/auth/OTPScreen";
import { SuperAdminDashboard } from "./components/dashboards/SuperAdminDashboard";
import { AdminDashboard } from "./components/dashboards/AdminDashboard";
import { SalesExecutiveDashboard } from "./components/dashboards/SalesExecutiveDashboard";
import { VendorDashboard } from "./components/dashboards/VendorDashboard";
import { CustomerDashboard } from "./components/dashboards/CustomerDashboard";
import { VendorOnboarding } from "./components/vendor/VendorOnboarding";
import { VendorOnboardingLanding } from "./components/vendor/VendorOnboardingLanding";
import { LandingPage } from "./components/landing/LandingPage";

export type UserRole = "super_admin" | "admin" | "sales_executive" | "vendor" | "customer";

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  isApproved?: boolean;
  avatar?: string;
  businessName?: string;
  profileComplete?: boolean;
}

export type AuthState = "landing" | "vendor_landing" | "welcome" | "login" | "register" | "otp" | "authenticated" | "vendor_onboarding";

export default function App() {
  const [authState, setAuthState] = useState<AuthState>("landing");
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState<UserRole>("customer");
  const [otpData, setOtpData] = useState<{ phone: string; email: string } | null>(null);
  const [isVendorRegistration, setIsVendorRegistration] = useState<boolean>(false);

  useEffect(() => {
    // Check if user is already logged in (localStorage simulation)
    const savedUser = localStorage.getItem("currentUser");
    if (savedUser) {
      const user = JSON.parse(savedUser);
      setCurrentUser(user);
      if (user.role === "vendor" && !user.isApproved) {
        setAuthState("vendor_onboarding");
      } else {
        setAuthState("authenticated");
      }
    }
  }, []);

  const handleLogin = (phone: string, email: string, role: UserRole) => {
    setSelectedRole(role);
    setOtpData({ phone, email });
    setIsVendorRegistration(false);
    setAuthState("otp");
  };

  const handleRegister = (userData: Partial<User>) => {
    setOtpData({ phone: userData.phone || "", email: userData.email || "" });
    setSelectedRole(userData.role || "customer");
    setIsVendorRegistration(userData.role === "vendor");
    setAuthState("otp");
  };

  const handleOTPVerification = (success: boolean) => {
    if (success && otpData) {
      const newUser: User = {
        id: Math.random().toString(36).substr(2, 9),
        name: "John Doe", // In real app, this would come from registration
        email: otpData.email,
        phone: otpData.phone,
        role: selectedRole,
        isApproved: selectedRole !== "vendor",
        profileComplete: selectedRole !== "vendor"
      };
      
      setCurrentUser(newUser);
      localStorage.setItem("currentUser", JSON.stringify(newUser));
      
      if (selectedRole === "vendor" && isVendorRegistration) {
        setAuthState("vendor_onboarding");
      } else {
        setAuthState("authenticated");
      }
    }
  };

  const handleVendorOnboardingComplete = () => {
    if (currentUser) {
      const updatedUser = { ...currentUser, isApproved: true };
      setCurrentUser(updatedUser);
      localStorage.setItem("currentUser", JSON.stringify(updatedUser));
      setAuthState("authenticated");
    }
  };

  const handleLogout = () => {
    setCurrentUser(null);
    localStorage.removeItem("currentUser");
    setAuthState("welcome");
  };

  if (authState === "landing") {
    return (
      <LandingPage 
        onGetStarted={() => setAuthState("welcome")} 
        onLogin={() => setAuthState("login")}
        onVendorOnboarding={() => setAuthState("vendor_landing")}
      />
    );
  }

  if (authState === "vendor_landing") {
    return (
      <VendorOnboardingLanding
        onGetStarted={() => {
          setSelectedRole("vendor");
          setAuthState("register");
        }}
        onLogin={() => setAuthState("login")}
      />
    );
  }

  if (authState === "welcome") {
    return <WelcomeScreen onGetStarted={() => setAuthState("login")} />;
  }

  if (authState === "login") {
    return (
      <LoginScreen
        onLogin={handleLogin}
        onRegister={() => setAuthState("register")}
      />
    );
  }

  if (authState === "register") {
    return (
      <RegisterScreen
        onRegister={handleRegister}
        onBackToLogin={() => setAuthState("login")}
      />
    );
  }

  if (authState === "otp") {
    return (
      <OTPScreen
        phone={otpData?.phone || ""}
        onVerification={handleOTPVerification}
        onBack={() => setAuthState("login")}
      />
    );
  }

  if (authState === "vendor_onboarding") {
    return (
      <VendorOnboarding
        user={currentUser!}
        onComplete={handleVendorOnboardingComplete}
        isRegistration={isVendorRegistration}
      />
    );
  }

  if (authState === "authenticated" && currentUser) {
    switch (currentUser.role) {
      case "super_admin":
        return <SuperAdminDashboard user={currentUser} onLogout={handleLogout} />;
      case "admin":
        return <AdminDashboard user={currentUser} onLogout={handleLogout} />;
      case "sales_executive":
        return <SalesExecutiveDashboard user={currentUser} onLogout={handleLogout} />;
      case "vendor":
        return <VendorDashboard user={currentUser} onLogout={handleLogout} />;
      case "customer":
        return <CustomerDashboard user={currentUser} onLogout={handleLogout} />;
      default:
        return <LandingPage onGetStarted={() => setAuthState("welcome")} onLogin={() => setAuthState("login")} />;
    }
  }

  return <LandingPage onGetStarted={() => setAuthState("welcome")} onLogin={() => setAuthState("login")} />;
}