import { But<PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Badge } from "../ui/badge";
import { 
  Store, 
  Users, 
  BarChart3, 
  Star, 
  CheckCircle, 
  ArrowRight,
  Building2,
  Zap,
  Shield,
  Headphones
} from "lucide-react";
import { ImageWithFallback } from "../figma/ImageWithFallback";

interface VendorOnboardingLandingProps {
  onGetStarted: () => void;
  onLogin: () => void;
}

export function VendorOnboardingLanding({ onGetStarted, onLogin }: VendorOnboardingLandingProps) {
  const features = [
    {
      icon: Store,
      title: "Business Profile",
      description: "Create a comprehensive business profile with photos, services, and contact information"
    },
    {
      icon: Users,
      title: "Customer Management",
      description: "Manage customer inquiries, bookings, and communications in one place"
    },
    {
      icon: BarChart3,
      title: "Analytics Dashboard",
      description: "Track your business performance with detailed analytics and insights"
    },
    {
      icon: Star,
      title: "Reviews & Ratings",
      description: "Build your reputation with customer reviews and ratings system"
    }
  ];

  const benefits = [
    {
      icon: Zap,
      title: "Quick Setup",
      description: "Get your business online in minutes with our streamlined onboarding process"
    },
    {
      icon: Shield,
      title: "Verified Listings",
      description: "All vendor profiles are verified for trust and credibility"
    },
    {
      icon: Headphones,
      title: "24/7 Support",
      description: "Get help whenever you need it with our dedicated support team"
    }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      business: "Johnson's Catering",
      rating: 5,
      comment: "VendorHub helped me reach more customers and grow my catering business by 150% in just 6 months!"
    },
    {
      name: "Mike Chen",
      business: "TechFix Solutions",
      rating: 5,
      comment: "The platform is incredibly easy to use. I get regular inquiries and the analytics help me understand my customers better."
    },
    {
      name: "Lisa Rodriguez",
      business: "Creative Designs Studio",
      rating: 5,
      comment: "Professional setup, great customer support, and excellent results. Highly recommended for any service business."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5">
      {/* Navigation */}
      <nav className="px-6 py-4 border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Building2 className="w-5 h-5 text-white" />
            </div>
            <span className="font-bold text-xl">VendorHub</span>
          </div>
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              onClick={onLogin}
              className="rounded-xl"
            >
              Already a member? Sign In
            </Button>
            <Button 
              onClick={onGetStarted}
              className="rounded-xl bg-primary hover:bg-primary/90 text-white"
            >
              Get Started
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="px-6 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge className="bg-accent/10 text-accent border-accent/20 rounded-full px-4 py-1">
                  Join 10,000+ Successful Vendors
                </Badge>
                <h1 className="text-4xl lg:text-5xl font-bold leading-tight">
                  Grow Your Business with{" "}
                  <span className="text-primary">VendorHub</span>
                </h1>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  Connect with thousands of customers, manage your business efficiently, 
                  and grow your revenue with our comprehensive vendor platform.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  onClick={onGetStarted}
                  size="lg"
                  className="rounded-xl bg-primary hover:bg-primary/90 text-white h-12 px-8"
                >
                  Start Your Journey
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
                <Button 
                  variant="outline" 
                  size="lg"
                  className="rounded-xl h-12 px-8"
                >
                  Watch Demo
                </Button>
              </div>

              <div className="flex items-center gap-8 pt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">10K+</div>
                  <div className="text-sm text-muted-foreground">Active Vendors</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">50K+</div>
                  <div className="text-sm text-muted-foreground">Monthly Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">4.9★</div>
                  <div className="text-sm text-muted-foreground">Average Rating</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden">
                <ImageWithFallback
                  src="https://images.unsplash.com/photo-1608222351212-18fe0ec7b13b?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxidXNpbmVzcyUyMGRhc2hib2FyZCUyMGFuYWx5dGljc3xlbnwxfHx8fHwxNzU2OTIzMDE3fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
                  alt="Business dashboard"
                  className="w-full h-[400px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              </div>
              
              {/* Floating cards */}
              <div className="absolute -bottom-4 -left-4 bg-white rounded-xl p-4 shadow-lg border">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-green-600">+150%</div>
                    <div className="text-xs text-muted-foreground">Revenue Growth</div>
                  </div>
                </div>
              </div>
              
              <div className="absolute -top-4 -right-4 bg-white rounded-xl p-4 shadow-lg border">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-blue-600">2.5K</div>
                    <div className="text-xs text-muted-foreground">New Customers</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="px-6 py-16 bg-white/50 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold">Everything You Need to Succeed</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our platform provides all the tools and features you need to manage and grow your business effectively.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="p-6 rounded-2xl border shadow-sm hover:shadow-md transition-shadow">
                <div className="space-y-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <feature.icon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="px-6 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1642522029691-029b5a432954?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxidXNpbmVzcyUyMG1lZXRpbmclMjBwcm9mZXNzaW9uYWx8ZW58MXx8fHwxNzU2OTIzMDk5fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
                alt="Business meeting"
                className="w-full h-[400px] object-cover rounded-2xl"
              />
            </div>

            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold">Why Choose VendorHub?</h2>
                <p className="text-lg text-muted-foreground">
                  Join thousands of successful vendors who trust VendorHub to grow their business.
                </p>
              </div>

              <div className="space-y-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex gap-4">
                    <div className="w-12 h-12 bg-accent/10 rounded-xl flex items-center justify-center flex-shrink-0">
                      <benefit.icon className="w-6 h-6 text-accent" />
                    </div>
                    <div>
                      <h3 className="font-semibold mb-1">{benefit.title}</h3>
                      <p className="text-muted-foreground">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>

              <Button 
                onClick={onGetStarted}
                size="lg"
                className="rounded-xl bg-primary hover:bg-primary/90 text-white h-12 px-8"
              >
                Join VendorHub Today
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="px-6 py-16 bg-white/50 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl font-bold">What Our Vendors Say</h2>
            <p className="text-lg text-muted-foreground">
              Hear from successful vendors who have grown their business with VendorHub.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6 rounded-2xl border shadow-sm">
                <div className="space-y-4">
                  <div className="flex items-center gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-muted-foreground italic">"{testimonial.comment}"</p>
                  <div>
                    <div className="font-semibold">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.business}</div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-6 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="p-12 rounded-2xl border shadow-lg bg-gradient-to-br from-primary/5 to-accent/5">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold">Ready to Grow Your Business?</h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Join VendorHub today and start connecting with customers, managing your business, and increasing your revenue.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  onClick={onGetStarted}
                  size="lg"
                  className="rounded-xl bg-primary hover:bg-primary/90 text-white h-12 px-8"
                >
                  Get Started Now
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
                <Button 
                  variant="outline" 
                  onClick={onLogin}
                  size="lg"
                  className="rounded-xl h-12 px-8"
                >
                  Already a member? Sign In
                </Button>
              </div>

              <div className="flex items-center justify-center gap-6 pt-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>No setup fees</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>30-day free trial</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>Cancel anytime</span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="px-6 py-8 border-t bg-white/80 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="w-6 h-6 bg-primary rounded-md flex items-center justify-center">
              <Building2 className="w-4 h-4 text-white" />
            </div>
            <span className="font-bold">VendorHub</span>
          </div>
          <p className="text-sm text-muted-foreground">
            © 2024 VendorHub. All rights reserved. Empowering businesses to grow and succeed.
          </p>
        </div>
      </footer>
    </div>
  );
}